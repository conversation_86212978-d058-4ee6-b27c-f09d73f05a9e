// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import { isRequestOptions } from "../../../../core.mjs";
export class TimeseriesGroups extends APIResource {
    arc(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.arc({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/arc', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    dkim(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.dkim({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/dkim', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    dmarc(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.dmarc({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/dmarc', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    malicious(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.malicious({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/malicious', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    spam(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.spam({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/spam', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    spf(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.spf({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/spf', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    spoof(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.spoof({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/spoof', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    threatCategory(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.threatCategory({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/threat_category', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    tlsVersion(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.tlsVersion({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/tls_version', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
}
//# sourceMappingURL=timeseries-groups.mjs.map