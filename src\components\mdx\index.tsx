/**
 * MDX Rendering Component
 *
 * This component provides a consistent way to render MDX content with custom
 * styling and component mappings. It uses Velite's MDX rendering capabilities
 * to render the compiled MDX code with custom React components.
 */

'use client'

import Image from 'next/image'
import { cn } from '@/lib/utils'
import { Video, YouTube, Bilibili, VideoGallery } from './video-components'

interface MdxProps {
  code: string        // Compiled MDX code from Velite
  className?: string  // Optional CSS classes to apply to the container
}

/**
 * Custom component mappings for MDX content
 *
 * These components replace default HTML elements in MDX with styled
 * versions that match our design system. Each component receives props
 * from the MDX renderer and applies consistent styling.
 */
const components = {
  // Replace img tags with Next.js optimized Image component
  img: (props: any) => (
    <Image
      {...props}
      alt={props.alt || ''}
      width={800}
      height={400}
      className="rounded-lg border"
    />
  ),

  // Styled heading components with consistent spacing and typography
  h1: (props: any) => (
    <h1 className="mt-8 mb-4 text-3xl font-bold tracking-tight" {...props} />
  ),
  h2: (props: any) => (
    <h2 className="mt-6 mb-3 text-2xl font-semibold tracking-tight" {...props} />
  ),
  h3: (props: any) => (
    <h3 className="mt-4 mb-2 text-xl font-semibold tracking-tight" {...props} />
  ),

  // Paragraph with consistent spacing and muted text color
  p: (props: any) => (
    <p className="mb-4 leading-7 text-muted-foreground" {...props} />
  ),
  // List components with proper indentation and spacing
  ul: (props: any) => (
    <ul className="mb-4 ml-6 list-disc space-y-2" {...props} />
  ),
  ol: (props: any) => (
    <ol className="mb-4 ml-6 list-decimal space-y-2" {...props} />
  ),
  li: (props: any) => (
    <li className="leading-7 text-muted-foreground" {...props} />
  ),

  // Blockquote with left border accent
  blockquote: (props: any) => (
    <blockquote
      className="mb-4 border-l-4 border-primary pl-4 italic text-muted-foreground"
      {...props}
    />
  ),

  // Inline code with background highlight
  code: (props: any) => (
    <code
      className="rounded bg-muted px-1.5 py-0.5 text-sm font-mono"
      {...props}
    />
  ),

  // Code blocks with syntax highlighting background
  pre: (props: any) => (
    <pre
      className="mb-4 overflow-x-auto rounded-lg bg-muted p-4 text-sm"
      {...props}
    />
  ),
  // Links with external link styling and security attributes
  a: (props: any) => (
    <a
      className="text-primary underline underline-offset-4 hover:text-primary/80"
      target="_blank"
      rel="noopener noreferrer"
      {...props}
    />
  ),

  // Table components with responsive wrapper and consistent borders
  table: (props: any) => (
    <div className="mb-4 overflow-x-auto">
      <table className="w-full border-collapse border border-border" {...props} />
    </div>
  ),
  th: (props: any) => (
    <th className="border border-border bg-muted p-2 text-left font-semibold" {...props} />
  ),
  td: (props: any) => (
    <td className="border border-border p-2" {...props} />
  ),

  // Video components for multimedia content
  Video: Video,
  YouTube: YouTube,
  Bilibili: Bilibili,
  VideoGallery: VideoGallery,
}

/**
 * Main MDX rendering component
 *
 * Takes compiled MDX code from Velite and renders it with our custom
 * component mappings. The prose classes provide additional typography styling.
 *
 * @param code - Compiled MDX code from Velite
 * @param className - Optional additional CSS classes
 */
export function Mdx({ code, className }: MdxProps) {
  // For Velite, we need to handle the MDX content differently
  // Since Velite provides the content as a string, we'll render it directly
  if (typeof code === 'string') {
    return (
      <div
        className={cn(
          'prose prose-gray max-w-none dark:prose-invert',
          'prose-headings:scroll-m-20 prose-headings:tracking-tight',
          'prose-h1:text-4xl prose-h1:font-extrabold prose-h1:lg:text-5xl',
          'prose-h2:border-b prose-h2:pb-2 prose-h2:text-3xl prose-h2:font-semibold prose-h2:tracking-tight prose-h2:first:mt-0',
          'prose-h3:text-2xl prose-h3:font-semibold prose-h3:tracking-tight',
          'prose-h4:text-xl prose-h4:font-semibold prose-h4:tracking-tight',
          'prose-p:leading-7 prose-p:[&:not(:first-child)]:mt-6',
          'prose-blockquote:mt-6 prose-blockquote:border-l-2 prose-blockquote:pl-6 prose-blockquote:italic',
          'prose-ul:my-6 prose-ul:ml-6 prose-ul:list-disc prose-ul:[&>li]:mt-2',
          'prose-ol:my-6 prose-ol:ml-6 prose-ol:list-decimal prose-ol:[&>li]:mt-2',
          'prose-li:my-2',
          'prose-table:my-6 prose-table:w-full prose-table:overflow-y-auto',
          'prose-tr:m-0 prose-tr:border-t prose-tr:p-0 prose-tr:even:bg-muted',
          'prose-th:border prose-th:px-4 prose-th:py-2 prose-th:text-left prose-th:font-bold prose-th:[&[align=center]]:text-center prose-th:[&[align=right]]:text-right',
          'prose-td:border prose-td:px-4 prose-td:py-2 prose-td:text-left prose-td:[&[align=center]]:text-center prose-td:[&[align=right]]:text-right',
          'prose-code:relative prose-code:rounded prose-code:bg-muted prose-code:px-[0.3rem] prose-code:py-[0.2rem] prose-code:font-mono prose-code:text-sm prose-code:font-semibold',
          'prose-pre:overflow-x-auto prose-pre:rounded-lg prose-pre:border prose-pre:bg-muted prose-pre:p-4',
          className
        )}
        dangerouslySetInnerHTML={{ __html: code }}
      />
    )
  }

  // Fallback for other content types
  return (
    <div className={cn('prose prose-gray max-w-none dark:prose-invert', className)}>
      {code}
    </div>
  )

  return (
    <div className={cn('prose prose-neutral dark:prose-invert max-w-none', className)}>
      <Component components={components} />
    </div>
  )
}
