/**
 * Modern MDX Rendering Component
 *
 * High-performance MDX renderer built for Content Collections.
 * Provides optimized rendering with custom component mappings,
 * enhanced styling, and improved performance characteristics.
 */

'use client'

import Image from 'next/image'
import { cn } from '@/lib/utils'
import { Video, YouTube, Bilibili, VideoGallery } from './video-components'

interface MdxProps {
  code: any           // Compiled MDX component from Content Collections
  className?: string  // Optional CSS classes to apply to the container
}

/**
 * Custom component mappings for MDX content
 *
 * These components replace default HTML elements in MDX with styled
 * versions that match our design system. Each component receives props
 * from the MDX renderer and applies consistent styling.
 */
const components = {
  // Replace img tags with Next.js optimized Image component
  img: (props: any) => (
    <Image
      {...props}
      alt={props.alt || ''}
      width={800}
      height={400}
      className="rounded-lg border"
    />
  ),

  // Styled heading components with consistent spacing and typography
  h1: (props: any) => (
    <h1 className="mt-8 mb-4 text-3xl font-bold tracking-tight" {...props} />
  ),
  h2: (props: any) => (
    <h2 className="mt-6 mb-3 text-2xl font-semibold tracking-tight" {...props} />
  ),
  h3: (props: any) => (
    <h3 className="mt-4 mb-2 text-xl font-semibold tracking-tight" {...props} />
  ),

  // Paragraph with consistent spacing and muted text color
  p: (props: any) => (
    <p className="mb-4 leading-7 text-muted-foreground" {...props} />
  ),
  // List components with proper indentation and spacing
  ul: (props: any) => (
    <ul className="mb-4 ml-6 list-disc space-y-2" {...props} />
  ),
  ol: (props: any) => (
    <ol className="mb-4 ml-6 list-decimal space-y-2" {...props} />
  ),
  li: (props: any) => (
    <li className="leading-7 text-muted-foreground" {...props} />
  ),

  // Blockquote with left border accent
  blockquote: (props: any) => (
    <blockquote
      className="mb-4 border-l-4 border-primary pl-4 italic text-muted-foreground"
      {...props}
    />
  ),

  // Inline code with background highlight
  code: (props: any) => (
    <code
      className="rounded bg-muted px-1.5 py-0.5 text-sm font-mono"
      {...props}
    />
  ),

  // Code blocks with syntax highlighting background
  pre: (props: any) => (
    <pre
      className="mb-4 overflow-x-auto rounded-lg bg-muted p-4 text-sm"
      {...props}
    />
  ),
  // Links with external link styling and security attributes
  a: (props: any) => (
    <a
      className="text-primary underline underline-offset-4 hover:text-primary/80"
      target="_blank"
      rel="noopener noreferrer"
      {...props}
    />
  ),

  // Table components with responsive wrapper and consistent borders
  table: (props: any) => (
    <div className="mb-4 overflow-x-auto">
      <table className="w-full border-collapse border border-border" {...props} />
    </div>
  ),
  th: (props: any) => (
    <th className="border border-border bg-muted p-2 text-left font-semibold" {...props} />
  ),
  td: (props: any) => (
    <td className="border border-border p-2" {...props} />
  ),

  // Video components for multimedia content
  Video: Video,
  YouTube: YouTube,
  Bilibili: Bilibili,
  VideoGallery: VideoGallery,
}

/**
 * Modern MDX Renderer with Content Collections Integration
 *
 * High-performance MDX renderer optimized for Content Collections.
 * Content Collections provides pre-compiled MDX as executable JavaScript code.
 *
 * @param code - Compiled MDX JavaScript code from Content Collections
 * @param className - Optional additional CSS classes
 */
export function Mdx({ code, className }: MdxProps) {
  // Content Collections provides MDX as executable JavaScript code
  // We need to safely evaluate it to get the React component
  let Component: React.ComponentType<any>

  try {
    // Content Collections MDX format: "var Component=(()=>{...})(); return Component;"
    // Let's use eval in a controlled way since the content is trusted
    const React = require('react')
    const jsxRuntime = require('react/jsx-runtime')

    // Set up the global context for MDX execution
    const originalJsxRuntime = (global as any)._jsx_runtime
    const originalReact = (global as any).React

    // Temporarily set globals
    ;(global as any)._jsx_runtime = jsxRuntime
    ;(global as any).React = React

    try {
      // Execute the MDX code directly
      Component = eval(`(function() { ${code} })()`)
    } finally {
      // Restore original globals
      if (originalJsxRuntime !== undefined) {
        ;(global as any)._jsx_runtime = originalJsxRuntime
      } else {
        delete (global as any)._jsx_runtime
      }
      if (originalReact !== undefined) {
        ;(global as any).React = originalReact
      } else {
        delete (global as any).React
      }
    }

    // Ensure we have a valid component
    if (typeof Component !== 'function') {
      console.log('Execution result type:', typeof Component)
      console.log('Execution result:', Component)
      throw new Error('MDX did not return a valid React component')
    }

  } catch (error) {
    console.error('Error rendering MDX:', error)
    console.log('MDX code preview:', typeof code === 'string' ? code.substring(0, 200) : code)

    // Fallback component for errors
    Component = () => (
      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
        <h3 className="text-red-600 mb-2">Content Rendering Error</h3>
        <p className="text-red-700 text-sm">
          There was an issue rendering this content. Please check the console for details.
        </p>
      </div>
    )
  }

  return (
    <div className={cn('prose prose-neutral dark:prose-invert max-w-none', className)}>
      <Component components={components} />
    </div>
  )
}
