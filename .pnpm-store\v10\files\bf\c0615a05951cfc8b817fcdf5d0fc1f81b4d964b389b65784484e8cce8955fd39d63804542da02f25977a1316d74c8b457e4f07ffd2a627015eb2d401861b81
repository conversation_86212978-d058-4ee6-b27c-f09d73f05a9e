// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import { isRequestOptions } from "../../../../core.mjs";
export class TimeseriesGroups extends APIResource {
    arc(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.arc({}, query);
        }
        return this._client.get('/radar/email/routing/timeseries_groups/arc', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    dkim(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.dkim({}, query);
        }
        return this._client.get('/radar/email/routing/timeseries_groups/dkim', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    dmarc(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.dmarc({}, query);
        }
        return this._client.get('/radar/email/routing/timeseries_groups/dmarc', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    encrypted(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.encrypted({}, query);
        }
        return this._client.get('/radar/email/routing/timeseries_groups/encrypted', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    ipVersion(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.ipVersion({}, query);
        }
        return this._client.get('/radar/email/routing/timeseries_groups/ip_version', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    spf(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.spf({}, query);
        }
        return this._client.get('/radar/email/routing/timeseries_groups/spf', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
}
//# sourceMappingURL=timeseries-groups.mjs.map