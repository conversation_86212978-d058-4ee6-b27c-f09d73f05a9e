/**
 * SEO Service
 * 
 * Service for generating SEO-related content including sitemaps,
 * RSS feeds, structured data, and meta tags.
 */

import type {
  ContentItem,
  ContentType,
  SitemapEntry,
  RSSFeed,
  RSSItem,
  StructuredData,
  BlogPostStructuredData,
  ProductStructuredData,
  OrganizationStructuredData,
  MetaTags,
  BlogContent,
  ProductContent,
  CaseStudyContent
} from '../types'

interface SEOConfig {
  baseUrl: string
  siteName: string
  siteDescription: string
  defaultLocale: string
  locales: string[]
  social: {
    twitter?: string
    facebook?: string
  }
}

export class SEOService {
  private config: SEOConfig
  
  constructor(config: SEOConfig) {
    this.config = config
  }
  
  /**
   * Generate sitemap entries from content
   */
  generateSitemap(content: ContentItem[]): SitemapEntry[] {
    const entries: SitemapEntry[] = []
    
    // Add static pages
    const staticPages = [
      { path: '', priority: 1.0, changefreq: 'daily' as const },
      { path: '/pricing', priority: 0.8, changefreq: 'weekly' as const },
      { path: '/showcase', priority: 0.8, changefreq: 'weekly' as const },
    ]
    
    staticPages.forEach(page => {
      this.config.locales.forEach(locale => {
        const url = locale === this.config.defaultLocale 
          ? `${this.config.baseUrl}${page.path}` 
          : `${this.config.baseUrl}/${locale}${page.path}`
        
        const alternates = this.config.locales.map(altLocale => ({
          hreflang: altLocale === 'zh' ? 'zh-CN' : 'en-US',
          href: altLocale === this.config.defaultLocale 
            ? `${this.config.baseUrl}${page.path}` 
            : `${this.config.baseUrl}/${altLocale}${page.path}`
        }))
        
        entries.push({
          url,
          changefreq: page.changefreq,
          priority: page.priority,
          alternates
        })
      })
    })
    
    // Add content pages
    content.forEach(item => {
      const alternates = this.config.locales
        .map(locale => {
          const altContent = content.find(c => 
            c.slug === item.slug && c.lang === locale
          )
          if (!altContent) return null
          
          return {
            hreflang: locale === 'zh' ? 'zh-CN' : 'en-US',
            href: `${this.config.baseUrl}${altContent.url}`
          }
        })
        .filter(Boolean) as Array<{ hreflang: string; href: string }>
      
      entries.push({
        url: `${this.config.baseUrl}${item.url}`,
        lastmod: item.updatedAt || item.publishedAt,
        changefreq: 'weekly',
        priority: item.featured ? 0.9 : 0.7,
        alternates: alternates.length > 1 ? alternates : undefined
      })
    })
    
    return entries
  }
  
  /**
   * Generate RSS feeds for blogs
   */
  generateRSSFeeds(blogs: BlogContent[]): Record<string, RSSFeed> {
    const feeds: Record<string, RSSFeed> = {}
    
    this.config.locales.forEach(locale => {
      const localBlogs = blogs
        .filter(blog => blog.lang === locale)
        .sort((a, b) => {
          const dateA = new Date(a.publishedAt || '').getTime()
          const dateB = new Date(b.publishedAt || '').getTime()
          return dateB - dateA
        })
        .slice(0, 20) // Limit to 20 most recent posts
      
      const items: RSSItem[] = localBlogs.map(blog => ({
        title: blog.title,
        description: blog.description,
        link: `${this.config.baseUrl}${blog.url}`,
        guid: `${this.config.baseUrl}${blog.url}`,
        pubDate: blog.publishedAt ? new Date(blog.publishedAt).toUTCString() : '',
        author: blog.author,
        category: blog.tags
      }))
      
      feeds[locale] = {
        title: `${this.config.siteName} Blog${locale === 'zh' ? ' - 中文' : ''}`,
        description: this.config.siteDescription,
        link: `${this.config.baseUrl}${locale === this.config.defaultLocale ? '' : `/${locale}`}`,
        language: locale === 'zh' ? 'zh-CN' : 'en-US',
        lastBuildDate: new Date().toUTCString(),
        items
      }
    })
    
    return feeds
  }
  
  /**
   * Generate structured data for content
   */
  generateStructuredData(content: ContentItem): StructuredData {
    const baseUrl = this.config.baseUrl
    
    if (content.url.includes('/blogs/')) {
      return this.generateBlogStructuredData(content as BlogContent)
    } else if (content.url.includes('/products/')) {
      return this.generateProductStructuredData(content as ProductContent)
    } else {
      // Default article structured data for case studies
      return {
        '@context': 'https://schema.org',
        '@type': 'Article',
        headline: content.title,
        description: content.description,
        url: `${baseUrl}${content.url}`,
        datePublished: content.publishedAt,
        dateModified: content.updatedAt || content.publishedAt,
        author: this.generateOrganizationStructuredData(),
        publisher: this.generateOrganizationStructuredData()
      }
    }
  }
  
  /**
   * Generate blog post structured data
   */
  private generateBlogStructuredData(blog: BlogContent): BlogPostStructuredData {
    return {
      '@context': 'https://schema.org',
      '@type': 'BlogPosting',
      headline: blog.title,
      description: blog.description,
      author: {
        '@type': 'Person',
        name: blog.author || 'ShipAny Team',
        ...(blog.authorImage && {
          image: {
            '@type': 'ImageObject',
            url: `${this.config.baseUrl}${blog.authorImage}`
          }
        })
      },
      datePublished: blog.publishedAt || '',
      dateModified: blog.updatedAt || blog.publishedAt || '',
      ...(blog.coverImage && {
        image: {
          '@type': 'ImageObject',
          url: `${this.config.baseUrl}${blog.coverImage.src}`,
          width: blog.coverImage.width,
          height: blog.coverImage.height
        }
      }),
      url: `${this.config.baseUrl}${blog.url}`,
      publisher: this.generateOrganizationStructuredData(),
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': `${this.config.baseUrl}${blog.url}`
      },
      ...(blog.tags && {
        keywords: blog.tags.join(', ')
      }),
      ...(blog.metadata && {
        wordCount: blog.metadata.words,
        timeRequired: `PT${blog.metadata.readingTime}M`
      })
    }
  }
  
  /**
   * Generate product structured data
   */
  private generateProductStructuredData(product: ProductContent): ProductStructuredData {
    return {
      '@context': 'https://schema.org',
      '@type': 'SoftwareApplication',
      name: product.title,
      description: product.description,
      url: `${this.config.baseUrl}${product.url}`,
      ...(product.coverImage && {
        image: `${this.config.baseUrl}${product.coverImage.src}`
      }),
      applicationCategory: 'BusinessApplication',
      operatingSystem: 'Web Browser',
      ...(product.price && {
        offers: {
          '@type': 'Offer',
          price: product.price.replace(/[^0-9.]/g, ''),
          priceCurrency: 'USD'
        }
      }),
      publisher: this.generateOrganizationStructuredData()
    }
  }
  
  /**
   * Generate organization structured data
   */
  private generateOrganizationStructuredData(): OrganizationStructuredData {
    return {
      '@type': 'Organization',
      name: this.config.siteName,
      url: this.config.baseUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${this.config.baseUrl}/logo.png`
      },
      description: this.config.siteDescription,
      ...(this.config.social && {
        sameAs: Object.values(this.config.social).filter(Boolean)
      })
    }
  }
  
  /**
   * Generate meta tags for content
   */
  generateMetaTags(content: ContentItem): MetaTags {
    const canonicalUrl = `${this.config.baseUrl}${content.url}`
    
    return {
      title: content.title,
      description: content.description,
      canonical: canonicalUrl,
      openGraph: {
        title: content.title,
        description: content.description,
        type: content.url.includes('/blogs/') ? 'article' : 'website',
        url: canonicalUrl,
        ...(content.coverImage && {
          images: [{
            url: `${this.config.baseUrl}${content.coverImage.src}`,
            width: content.coverImage.width,
            height: content.coverImage.height,
            alt: content.title
          }]
        }),
        ...(content.publishedAt && {
          publishedTime: content.publishedAt
        }),
        ...(content.updatedAt && {
          modifiedTime: content.updatedAt
        }),
        ...(content.tags && {
          tags: content.tags
        })
      },
      twitter: {
        card: 'summary_large_image',
        title: content.title,
        description: content.description,
        ...(content.coverImage && {
          images: [`${this.config.baseUrl}${content.coverImage.src}`]
        }),
        ...(this.config.social?.twitter && {
          site: this.config.social.twitter
        })
      }
    }
  }
}
