import { APIResource } from "../../../../resource.js";
import * as BytimesAPI from "./bytimes.js";
import { BytimeGetParams, BytimeGetResponse, Bytimes } from "./bytimes.js";
import * as SummariesAPI from "./summaries.js";
import { Summaries, SummaryGetParams, SummaryGetResponse } from "./summaries.js";
export declare class Events extends APIResource {
    bytimes: BytimesAPI.Bytimes;
    summaries: SummariesAPI.Summaries;
}
export type Dimension = 'event' | 'appID' | 'coloName' | 'ipVersion';
export type DimensionParam = 'event' | 'appID' | 'coloName' | 'ipVersion';
export declare namespace Events {
    export { type Dimension as Dimension };
    export { Bytimes as Bytimes, type BytimeGetResponse as BytimeGetResponse, type BytimeGetParams as BytimeGetParams, };
    export { Summaries as Summaries, type SummaryGetResponse as SummaryGetResponse, type SummaryGetParams as SummaryGetParams, };
}
//# sourceMappingURL=events.d.ts.map