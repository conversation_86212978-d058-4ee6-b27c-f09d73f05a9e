import { allBlogs } from 'content-collections'
import { notFound } from 'next/navigation'
import { setRequestLocale } from 'next-intl/server'
import { Mdx } from '@/components/mdx'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import Image from 'next/image'
import { Metadata } from 'next'
import ContentLanguageIndicator from '@/components/locale/content-language-indicator'

export async function generateStaticParams() {
  return allBlogs.map(blog => ({ 
    locale: blog.lang, 
    slug: blog.slug 
  }))
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>
}): Promise<Metadata> {
  const { locale, slug } = await params
  
  const blog = allBlogs.find(b => b.lang === locale && b.slug === slug)

  if (!blog) {
    return {
      title: 'Blog Post Not Found',
    }
  }

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/blogs/${slug}`

  if (locale !== 'en') {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/blogs/${slug}`
  }

  return {
    title: blog.title,
    description: blog.description,
    alternates: {
      canonical: canonicalUrl,
    },
  }
}

export default async function BlogPostPage({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>
}) {
  const { locale, slug } = await params
  setRequestLocale(locale)

  const blog = allBlogs.find(b => b.lang === locale && b.slug === slug)

  if (!blog) {
    notFound()
  }

  return (
    <section className="py-16">
      <div className="container">
        <div className="mx-auto max-w-4xl">
          {/* Header */}
          <div className="mb-8">
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                {blog.featured && (
                  <Badge variant="secondary">Featured</Badge>
                )}
                {blog.tags && blog.tags.map((tag) => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
              <ContentLanguageIndicator variant="compact" />
            </div>
            
            <h1 className="mb-4 text-4xl font-bold tracking-tight">
              {blog.title}
            </h1>
            
            {blog.description && (
              <p className="text-xl text-muted-foreground">
                {blog.description}
              </p>
            )}

            {(blog.author || blog.publishedAt) && (
              <div className="mt-6 flex items-center gap-4 text-sm text-muted-foreground">
                {blog.author && (
                  <div className="flex items-center gap-2">
                    {blog.authorImage && (
                      <Image
                        src={blog.authorImage}
                        alt={blog.author}
                        width={24}
                        height={24}
                        className="rounded-full"
                      />
                    )}
                    <span>By {blog.author}</span>
                  </div>
                )}
                {blog.publishedAt && (
                  <time dateTime={blog.publishedAt}>
                    {new Date(blog.publishedAt).toLocaleDateString(locale, {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </time>
                )}
              </div>
            )}
          </div>

          {/* Cover Image */}
          {blog.coverImage && (
            <div className="mb-8">
              <Image
                src={blog.coverImage}
                alt={blog.title}
                width={800}
                height={400}
                className="w-full rounded-lg border object-cover"
              />
            </div>
          )}

          {/* Content */}
          <Card>
            <CardContent className="p-8">
              <Mdx code={blog.mdx} />
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
