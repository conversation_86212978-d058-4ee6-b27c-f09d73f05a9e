/**
 * Content Collections Configuration
 *
 * This file configures Content Collections to process MDX files for our multi-language
 * content management system. Content Collections transforms MDX files into type-safe
 * TypeScript objects that can be imported and used throughout the application.
 *
 * Key Features:
 * - Multi-language support (en/zh) with automatic language detection
 * - Unified schema for blogs, products, and case studies
 * - Automatic URL generation and metadata extraction
 * - Type-safe content access with full TypeScript support
 * - File system-based content organization
 * - Enhanced MDX processing with compilation support
 *
 * Directory Structure:
 * content/
 * ├── blogs/
 * │   ├── en/*.mdx
 * │   └── zh/*.mdx
 * ├── products/
 * │   ├── en/*.mdx
 * │   └── zh/*.mdx
 * └── case-studies/
 *     ├── en/*.mdx
 *     └── zh/*.mdx
 */

// Content Collections Core Functions
import { defineCollection, defineConfig } from "@content-collections/core";
import { compileMDX } from "@content-collections/mdx";
// Zod for schema validation
import { z } from "zod";
// Node.js File System Modules - For file metadata extraction
import fs from 'fs';
import path from 'path';

/**
 * Common schema definition for all content types
 *
 * This schema defines the structure and validation rules for frontmatter fields
 * that are common across all content types (blogs, products, case studies).
 * It ensures consistency and type safety across the entire content system.
 */
const commonSchema = z.object({
  // Required Fields
  title: z.string(),                                    // Content title for display
  slug: z.string(),                                     // URL slug for routing

  // SEO and Metadata Fields
  description: z.string().optional(),                   // Meta description for SEO
  coverImage: z.string().optional(),                    // Hero/cover image URL

  // Author Information
  author: z.string().optional(),                        // Author name
  authorImage: z.string().optional(),                   // Author avatar URL

  // Publishing and Organization
  publishedAt: z.date().optional(),                     // Publication date
  featured: z.boolean().default(false),                 // Featured content flag
  tags: z.array(z.string()).default([]),               // Content tags/categories

  // Video Content Support
  videoUrl: z.string().optional(),                      // Self-hosted video URL
  videoThumbnail: z.string().optional(),               // Video thumbnail/poster image
  videoDuration: z.string().optional(),                // Video duration (e.g., "5:30")
});

/**
 * Factory function to create content collection configuration
 *
 * This function creates a reusable configuration object for different content types
 * (blogs, products, case studies) with consistent field definitions and computed
 * properties. It ensures all content types have the same structure and capabilities.
 *
 * @param name - The collection name for TypeScript generation (e.g., 'blogs', 'products')
 * @param directory - The directory path to scan for files (e.g., 'content/blogs')
 * @returns Collection configuration object for Content Collections
 */
const createContentCollection = (name: string, directory: string) => {
  return defineCollection({
    name,
    // Directory to scan for content files
    directory,
    // Include all MDX files in subdirectories
    include: "**/*.mdx",
    // Schema validation using Zod
    schema: commonSchema,
    // Transform function to process content and add computed fields
    transform: async (document, context) => {
      // Compile MDX content for runtime rendering
      const mdx = await compileMDX(context, document);
      
      // Extract language from file path (e.g., 'blogs/en/post.mdx' -> 'en')
      const pathParts = document._meta.path.split('/');
      const lang = pathParts[pathParts.length - 2]; // Second to last part is language
      
      // Generate URL based on content type, language, and slug
      const contentType = directory.split('/')[1]; // Extract content type from directory
      const url = `/${lang}/${contentType}/${document.slug}`;
      
      // Get file creation time as fallback for publishedAt
      let createdAt: Date;
      try {
        const filePath = path.join(process.cwd(), directory, document._meta.path);
        const stats = fs.statSync(filePath);
        createdAt = stats.birthtime;
      } catch (error) {
        // Fallback to current date if file stats are unavailable
        createdAt = new Date();
      }

      return {
        ...document,
        // Computed fields
        lang,                                           // Language code extracted from path
        url,                                           // Full URL path for the content
        mdx,                                           // Compiled MDX for rendering
        createdAt,                                     // File creation timestamp
        // Use publishedAt if provided, otherwise fall back to createdAt
        publishedAt: document.publishedAt || createdAt,
      };
    },
  });
};

/**
 * Content Collection Definitions
 *
 * These create specific collections for each content category using the
 * common configuration. Each collection will have its own TypeScript interface
 * generated by Content Collections for type-safe content access.
 */
const blogs = createContentCollection("blogs", "content/blogs");
const products = createContentCollection("products", "content/products");
const caseStudies = createContentCollection("caseStudies", "content/case-studies");

/**
 * Main Content Collections Configuration
 *
 * This is the primary configuration object that tells Content Collections how to process
 * content files. It specifies which collections to generate and process. When Content
 * Collections runs, it will:
 *
 * 1. Scan the specified directories for MDX files
 * 2. Validate content against the defined schemas
 * 3. Process frontmatter and content according to transform functions
 * 4. Generate TypeScript types and data objects
 * 5. Create the .content-collections/generated directory with all processed content
 *
 * The generated content can then be imported and used throughout the application
 * with full TypeScript support and type safety.
 */
export default defineConfig({
  collections: [blogs, products, caseStudies],
});
