/**
 * Content Collections Configuration
 *
 * This file configures Content Collections to process MDX files for our multi-language
 * content management system. Content Collections transforms MDX files into type-safe
 * TypeScript objects that can be imported and used throughout the application.
 *
 * Key Features:
 * - Multi-language support (en/zh) with automatic language detection
 * - Unified schema for blogs, products, and case studies
 * - Automatic URL generation and metadata extraction
 * - Type-safe content access with full TypeScript support
 * - File system-based content organization
 * - Enhanced MDX processing with compilation support
 *
 * Directory Structure:
 * content/
 * ├── blogs/
 * │   ├── en/*.mdx
 * │   └── zh/*.mdx
 * ├── products/
 * │   ├── en/*.mdx
 * │   └── zh/*.mdx
 * └── case-studies/
 *     ├── en/*.mdx
 *     └── zh/*.mdx
 */

import { defineCollection, defineConfig } from "@content-collections/core";
import { compileMDX } from "@content-collections/mdx";
import { z } from "zod";

/**
 * Common schema for all content types
 */
const contentSchema = z.object({
  title: z.string(),
  slug: z.string(),
  description: z.string().optional(),
  coverImage: z.string().optional(),
  author: z.string().optional(),
  authorImage: z.string().optional(),
  publishedAt: z.coerce.date().optional(),
  featured: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  videoUrl: z.string().optional(),
  videoThumbnail: z.string().optional(),
  videoDuration: z.string().optional(),
});

/**
 * Blogs Collection
 */
const blogs = defineCollection({
  name: "blogs",
  directory: "content/blogs",
  include: "**/*.mdx",
  schema: contentSchema,
  transform: async (document, context) => {
    // Compile MDX content for runtime rendering
    const mdx = await compileMDX(context, document);

    // Extract language from file path (e.g., 'en/post.mdx' -> 'en')
    const pathParts = document._meta.path.split('/');
    const lang = pathParts[0]; // First part is language

    // Generate URL based on content type, language, and slug
    const url = `/${lang}/blogs/${document.slug}`;

    return {
      ...document,
      lang,
      url,
      mdx,
      publishedAt: document.publishedAt || new Date(),
    };
  },
});

/**
 * Products Collection
 */
const products = defineCollection({
  name: "products",
  directory: "content/products",
  include: "**/*.mdx",
  schema: contentSchema,
  transform: async (document, context) => {
    const mdx = await compileMDX(context, document);
    const pathParts = document._meta.path.split('/');
    const lang = pathParts[0];
    const url = `/${lang}/products/${document.slug}`;

    return {
      ...document,
      lang,
      url,
      mdx,
      publishedAt: document.publishedAt || new Date(),
    };
  },
});

/**
 * Case Studies Collection
 */
const caseStudies = defineCollection({
  name: "caseStudies",
  directory: "content/case-studies",
  include: "**/*.mdx",
  schema: contentSchema,
  transform: async (document, context) => {
    const mdx = await compileMDX(context, document);
    const pathParts = document._meta.path.split('/');
    const lang = pathParts[0];
    const url = `/${lang}/case-studies/${document.slug}`;

    return {
      ...document,
      lang,
      url,
      mdx,
      publishedAt: document.publishedAt || new Date(),
    };
  },
});

/**
 * Main Content Collections Configuration
 *
 * This is the primary configuration object that tells Content Collections how to process
 * content files. It specifies which collections to generate and process. When Content
 * Collections runs, it will:
 *
 * 1. Scan the specified directories for MDX files
 * 2. Validate content against the defined schemas
 * 3. Process frontmatter and content according to transform functions
 * 4. Generate TypeScript types and data objects
 * 5. Create the .content-collections/generated directory with all processed content
 *
 * The generated content can then be imported and used throughout the application
 * with full TypeScript support and type safety.
 */
export default defineConfig({
  collections: [blogs, products, caseStudies],
});
