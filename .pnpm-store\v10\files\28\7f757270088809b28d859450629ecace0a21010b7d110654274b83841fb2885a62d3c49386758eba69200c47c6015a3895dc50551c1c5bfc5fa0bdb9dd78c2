{"version": 3, "file": "datasets.d.ts", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/dlp/datasets/datasets.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AACzC,OAAO,KAAK,SAAS,MAAM,UAAU,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,IAAI,eAAe,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,MAAM,UAAU,CAAC;AACvG,OAAO,KAAK,WAAW,MAAM,qBAAqB,CAAC;AACnD,OAAO,EACL,mBAAmB,EACnB,qBAAqB,EACrB,gCAAgC,EAChC,QAAQ,EACT,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AAEpD,qBAAa,QAAS,SAAQ,WAAW;IACvC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAsC;IAC9D,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAA0C;IAExE;;;;;;;;;;;OAWG;IACH,MAAM,CAAC,MAAM,EAAE,mBAAmB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;IASpG;;;;;;;;;;OAUG;IACH,MAAM,CACJ,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,mBAAmB,EAC3B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IAU3B;;;;;;;;;;;;OAYG;IACH,IAAI,CACF,MAAM,EAAE,iBAAiB,EACzB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,OAAO,CAAC;IAKhD;;;;;;;;;;OAUG;IACH,MAAM,CACJ,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,mBAAmB,EAC3B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;IAQxB;;;;;;;;;;OAUG;IACH,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;CAQ1G;AAED,qBAAa,kBAAmB,SAAQ,UAAU,CAAC,OAAO,CAAC;CAAG;AAE9D,MAAM,WAAW,OAAO;IACtB,EAAE,EAAE,MAAM,CAAC;IAEX,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAE/B,UAAU,EAAE,MAAM,CAAC;IAEnB,gBAAgB,EAAE,MAAM,CAAC;IAEzB,IAAI,EAAE,MAAM,CAAC;IAEb,SAAS,EAAE,MAAM,CAAC;IAElB,MAAM,EAAE,OAAO,CAAC;IAEhB,MAAM,EAAE,OAAO,GAAG,WAAW,GAAG,SAAS,GAAG,YAAY,GAAG,QAAQ,GAAG,UAAU,CAAC;IAEjF;;;;OAIG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAE/B,cAAc,CAAC,EAAE,OAAO,CAAC;IAEzB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B;AAED,yBAAiB,OAAO,CAAC;IACvB,UAAiB,MAAM;QACrB,QAAQ,EAAE,MAAM,CAAC;QAEjB,WAAW,EAAE,MAAM,CAAC;QAEpB,SAAS,EAAE,MAAM,CAAC;QAElB,aAAa,EAAE,OAAO,GAAG,WAAW,GAAG,SAAS,GAAG,YAAY,GAAG,QAAQ,GAAG,UAAU,CAAC;KACzF;IAED,UAAiB,MAAM;QACrB,SAAS,EAAE,MAAM,CAAC;QAElB,MAAM,EAAE,OAAO,GAAG,WAAW,GAAG,SAAS,GAAG,YAAY,GAAG,QAAQ,GAAG,UAAU,CAAC;QAEjF,OAAO,EAAE,MAAM,CAAC;KACjB;CACF;AAED,MAAM,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;AAE1C,MAAM,WAAW,eAAe;IAC9B,OAAO,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,gBAAgB,EAAE,MAAM,CAAC;IAEzB,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;;OAGG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;;;OAIG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IAEzB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5B;;;;;;;OAOG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAE1B;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;;;OAKG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IAEzB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5B;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACtB;AAED,MAAM,WAAW,iBAAiB;IAChC,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,mBAAmB;IAClC,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,gBAAgB;IAC/B,UAAU,EAAE,MAAM,CAAC;CACpB;AAOD,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,OAAO,EACL,KAAK,OAAO,IAAI,OAAO,EACvB,KAAK,YAAY,IAAI,YAAY,EACjC,KAAK,eAAe,IAAI,eAAe,EACvC,kBAAkB,IAAI,kBAAkB,EACxC,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,gBAAgB,IAAI,gBAAgB,GAC1C,CAAC;IAEF,OAAO,EACL,eAAe,IAAI,MAAM,EACzB,KAAK,UAAU,IAAI,UAAU,EAC7B,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,gBAAgB,IAAI,gBAAgB,GAC1C,CAAC;IAEF,OAAO,EACL,QAAQ,IAAI,QAAQ,EACpB,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,gCAAgC,IAAI,gCAAgC,EACpE,KAAK,mBAAmB,IAAI,mBAAmB,GAChD,CAAC;CACH"}