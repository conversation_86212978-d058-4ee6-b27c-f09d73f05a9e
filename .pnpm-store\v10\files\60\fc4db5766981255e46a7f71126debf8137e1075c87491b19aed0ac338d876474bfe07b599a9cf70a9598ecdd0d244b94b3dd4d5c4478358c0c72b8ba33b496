"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeseriesGroups = void 0;
const resource_1 = require("../../../../resource.js");
const core_1 = require("../../../../core.js");
class TimeseriesGroups extends resource_1.APIResource {
    bitrate(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.bitrate({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/bitrate', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    duration(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.duration({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/duration', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    industry(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.industry({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/industry', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    ipVersion(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.ipVersion({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/ip_version', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    protocol(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.protocol({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/protocol', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    vector(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.vector({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/vector', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    vertical(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.vertical({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/vertical', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
}
exports.TimeseriesGroups = TimeseriesGroups;
//# sourceMappingURL=timeseries-groups.js.map