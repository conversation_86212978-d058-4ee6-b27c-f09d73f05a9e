import { APIResource } from "../../../../resource.js";
import * as UploadAPI from "./upload.js";
import { Upload, UploadCreateParams, UploadCreateResponse } from "./upload.js";
export declare class Assets extends APIResource {
    upload: UploadAPI.Upload;
}
export declare namespace Assets {
    export { Upload as Upload, type UploadCreateResponse as UploadCreateResponse, type UploadCreateParams as UploadCreateParams, };
}
//# sourceMappingURL=assets.d.ts.map