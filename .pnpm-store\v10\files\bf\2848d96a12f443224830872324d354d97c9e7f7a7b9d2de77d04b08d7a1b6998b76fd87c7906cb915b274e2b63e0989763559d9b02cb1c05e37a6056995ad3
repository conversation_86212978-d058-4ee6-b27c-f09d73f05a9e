{"version": 3, "file": "configurations.d.ts", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/gateway/configurations/configurations.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AACzC,OAAO,KAAK,oBAAoB,MAAM,sBAAsB,CAAC;AAC7D,OAAO,EAAE,iBAAiB,EAAE,0BAA0B,EAAE,MAAM,sBAAsB,CAAC;AAErF,qBAAa,cAAe,SAAQ,WAAW;IAC7C,iBAAiB,EAAE,oBAAoB,CAAC,iBAAiB,CAEvD;IAEF;;;;;;;;;;OAUG;IACH,MAAM,CACJ,MAAM,EAAE,yBAAyB,EACjC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,2BAA2B,CAAC;IAU/C;;;;;;;;;;;;;;OAcG;IACH,IAAI,CACF,MAAM,EAAE,uBAAuB,EAC/B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC;IAU7C;;;;;;;;;;OAUG;IACH,GAAG,CACD,MAAM,EAAE,sBAAsB,EAC9B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC;CAQ7C;AAED;;GAEG;AACH,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,sBAAsB,CAAC,EAAE,OAAO,CAAC;IAEjC;;OAEG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAE/B;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IAEtB;;;OAGG;IACH,qBAAqB,CAAC,EAAE,oBAAoB,GAAG,IAAI,CAAC;CACrD;AAED;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,sBAAsB,CAAC,EAAE,OAAO,CAAC;IAEjC;;OAEG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAE/B;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IAEtB;;;OAGG;IACH,qBAAqB,CAAC,EAAE,yBAAyB,GAAG,IAAI,CAAC;CAC1D;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAE1B;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAE1B;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;OAGG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;OAGG;IACH,IAAI,CAAC,EAAE,uBAAuB,GAAG,cAAc,CAAC;IAEhD;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;;OAGG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IAEpB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;OAGG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAE1B;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAE1B;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAE1B;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;OAGG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;OAGG;IACH,IAAI,CAAC,EAAE,uBAAuB,GAAG,cAAc,CAAC;IAEhD;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;;OAGG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAE1B;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;CAC1B;AAED;;GAEG;AACH,MAAM,WAAW,yBAAyB;IACxC;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;CAC1B;AAED;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC;;OAEG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAE/B;;OAEG;IACH,6BAA6B,CAAC,EAAE,OAAO,CAAC;CACzC;AAED;;GAEG;AACH,MAAM,WAAW,6BAA6B;IAC5C;;OAEG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAE/B;;OAEG;IACH,6BAA6B,CAAC,EAAE,OAAO,CAAC;CACzC;AAED;;;GAGG;AACH,MAAM,WAAW,yBAAyB;IACxC;;OAEG;IACH,OAAO,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED;;;GAGG;AACH,MAAM,WAAW,8BAA8B;IAC7C;;OAEG;IACH,OAAO,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;CACb;AAED;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACpC;;;OAGG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;;OAGG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IAEpB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;CACzB;AAED;;GAEG;AACH,MAAM,WAAW,0BAA0B;IACzC;;;OAGG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B;;OAEG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;CACf;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;CACf;AAED;;GAEG;AACH,MAAM,WAAW,4BAA4B;IAC3C;;OAEG;IACH,YAAY,CAAC,EAAE,mBAAmB,GAAG,IAAI,CAAC;IAE1C;;OAEG;IACH,SAAS,CAAC,EAAE,iBAAiB,GAAG,IAAI,CAAC;IAErC;;OAEG;IACH,UAAU,CAAC,EAAE,iBAAiB,GAAG,IAAI,CAAC;IAEtC;;OAEG;IACH,aAAa,CAAC,EAAE,oBAAoB,GAAG,IAAI,CAAC;IAE5C;;OAEG;IACH,iBAAiB,CAAC,EAAE,wBAAwB,GAAG,IAAI,CAAC;IAEpD;;;OAGG;IACH,WAAW,CAAC,EAAE,4BAA4B,CAAC,WAAW,GAAG,IAAI,CAAC;IAE9D;;;OAGG;IACH,kBAAkB,CAAC,EAAE,yBAAyB,GAAG,IAAI,CAAC;IAEtD;;OAEG;IACH,uBAAuB,CAAC,EAAE,qBAAqB,GAAG,IAAI,CAAC;IAEvD;;OAEG;IACH,IAAI,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC;IAE3B;;OAEG;IACH,aAAa,CAAC,EAAE,4BAA4B,CAAC,YAAY,GAAG,IAAI,CAAC;IAEjE;;OAEG;IACH,kBAAkB,CAAC,EAAE,iBAAiB,GAAG,IAAI,CAAC;IAE9C;;OAEG;IACH,OAAO,CAAC,EAAE,4BAA4B,CAAC,OAAO,GAAG,IAAI,CAAC;IAEtD;;OAEG;IACH,WAAW,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC;CAClC;AAED,yBAAiB,4BAA4B,CAAC;IAC5C;;;OAGG;IACH,UAAiB,WAAW;QAC1B;;;;WAIG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;IAED;;OAEG;IACH,UAAiB,YAAY;QAC3B;;WAEG;QACH,OAAO,CAAC,EAAE,OAAO,CAAC;KACnB;IAED;;OAEG;IACH,UAAiB,OAAO;QACtB;;WAEG;QACH,OAAO,CAAC,EAAE,OAAO,CAAC;QAElB;;WAEG;QACH,eAAe,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC;KACrC;CACF;AAED;;GAEG;AACH,MAAM,WAAW,iCAAiC;IAChD;;OAEG;IACH,YAAY,CAAC,EAAE,wBAAwB,GAAG,IAAI,CAAC;IAE/C;;OAEG;IACH,SAAS,CAAC,EAAE,sBAAsB,GAAG,IAAI,CAAC;IAE1C;;OAEG;IACH,UAAU,CAAC,EAAE,sBAAsB,GAAG,IAAI,CAAC;IAE3C;;OAEG;IACH,aAAa,CAAC,EAAE,yBAAyB,GAAG,IAAI,CAAC;IAEjD;;OAEG;IACH,iBAAiB,CAAC,EAAE,6BAA6B,GAAG,IAAI,CAAC;IAEzD;;;OAGG;IACH,WAAW,CAAC,EAAE,iCAAiC,CAAC,WAAW,GAAG,IAAI,CAAC;IAEnE;;;OAGG;IACH,kBAAkB,CAAC,EAAE,8BAA8B,GAAG,IAAI,CAAC;IAE3D;;OAEG;IACH,uBAAuB,CAAC,EAAE,0BAA0B,GAAG,IAAI,CAAC;IAE5D;;OAEG;IACH,IAAI,CAAC,EAAE,iBAAiB,GAAG,IAAI,CAAC;IAEhC;;OAEG;IACH,aAAa,CAAC,EAAE,iCAAiC,CAAC,YAAY,GAAG,IAAI,CAAC;IAEtE;;OAEG;IACH,kBAAkB,CAAC,EAAE,sBAAsB,GAAG,IAAI,CAAC;IAEnD;;OAEG;IACH,OAAO,CAAC,EAAE,iCAAiC,CAAC,OAAO,GAAG,IAAI,CAAC;IAE3D;;OAEG;IACH,WAAW,CAAC,EAAE,gBAAgB,GAAG,IAAI,CAAC;CACvC;AAED,yBAAiB,iCAAiC,CAAC;IACjD;;;OAGG;IACH,UAAiB,WAAW;QAC1B;;;;WAIG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;IAED;;OAEG;IACH,UAAiB,YAAY;QAC3B;;WAEG;QACH,OAAO,CAAC,EAAE,OAAO,CAAC;KACnB;IAED;;OAEG;IACH,UAAiB,OAAO;QACtB;;WAEG;QACH,OAAO,CAAC,EAAE,OAAO,CAAC;QAElB;;WAEG;QACH,eAAe,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC;KACrC;CACF;AAED;;;GAGG;AACH,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;OAEG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAE1B;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IAEb;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB;AAED;;;GAGG;AACH,MAAM,WAAW,yBAAyB;IACxC;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;OAEG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAE1B;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IAEb;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,2BAA2B;IAC1C,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,QAAQ,CAAC,EAAE,4BAA4B,CAAC;IAExC,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,yBAAyB;IACxC,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,QAAQ,CAAC,EAAE,4BAA4B,CAAC;IAExC,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,QAAQ,CAAC,EAAE,4BAA4B,CAAC;IAExC,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,yBAAyB;IACxC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,QAAQ,CAAC,EAAE,iCAAiC,CAAC;CAC9C;AAED,MAAM,WAAW,uBAAuB;IACtC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,QAAQ,CAAC,EAAE,iCAAiC,CAAC;CAC9C;AAED,MAAM,WAAW,sBAAsB;IACrC,UAAU,EAAE,MAAM,CAAC;CACpB;AAID,MAAM,CAAC,OAAO,WAAW,cAAc,CAAC;IACtC,OAAO,EACL,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,wBAAwB,IAAI,wBAAwB,EACzD,KAAK,yBAAyB,IAAI,yBAAyB,EAC3D,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,YAAY,IAAI,YAAY,EACjC,KAAK,4BAA4B,IAAI,4BAA4B,EACjE,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,WAAW,IAAI,WAAW,EAC/B,KAAK,2BAA2B,IAAI,2BAA2B,EAC/D,KAAK,yBAAyB,IAAI,yBAAyB,EAC3D,KAAK,wBAAwB,IAAI,wBAAwB,EACzD,KAAK,yBAAyB,IAAI,yBAAyB,EAC3D,KAAK,uBAAuB,IAAI,uBAAuB,EACvD,KAAK,sBAAsB,IAAI,sBAAsB,GACtD,CAAC;IAEF,OAAO,EACL,iBAAiB,IAAI,iBAAiB,EACtC,KAAK,0BAA0B,IAAI,0BAA0B,GAC9D,CAAC;CACH"}