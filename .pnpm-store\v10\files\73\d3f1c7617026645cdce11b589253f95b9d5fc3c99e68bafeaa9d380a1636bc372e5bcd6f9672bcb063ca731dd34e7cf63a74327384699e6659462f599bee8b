{"version": 3, "file": "policies.js", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/access/applications/policies.ts"], "names": [], "mappings": ";AAAA,sFAAsF;;;AAEtF,sDAAmD;AACnD,8CAAoD;AAIpD,gDAAoD;AACpD,0DAAoD;AAEpD,MAAa,QAAS,SAAQ,sBAAW;IACvC;;;;;;;;;;;;;;OAcG;IACH,MAAM,CACJ,KAAa,EACb,MAA0B,EAC1B,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QAChD,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,uBAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,uBAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OACE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,aAAa,IAAI,eAAe,gBAAgB,KAAK,WAAW,EAAE;YACtF,IAAI;YACJ,GAAG,OAAO;SACX,CACF,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,MAAM,CACJ,KAAa,EACb,QAAgB,EAChB,MAA0B,EAC1B,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QAChD,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,uBAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,uBAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OACE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,aAAa,IAAI,eAAe,gBAAgB,KAAK,aAAa,QAAQ,EAAE,EAAE;YACjG,IAAI;YACJ,GAAG,OAAO;SACX,CACF,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IA0BD,IAAI,CACF,KAAa,EACb,SAAiD,EAAE,EACnD,OAA6B;QAE7B,IAAI,IAAA,uBAAgB,EAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SACrC;QACD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QACvC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,uBAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,uBAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAC5B,IAAI,aAAa,IAAI,eAAe,gBAAgB,KAAK,WAAW,EACpE,6BAA6B,EAC7B,OAAO,CACR,CAAC;IACJ,CAAC;IA2BD,MAAM,CACJ,KAAa,EACb,QAAgB,EAChB,SAAmD,EAAE,EACrD,OAA6B;QAE7B,IAAI,IAAA,uBAAgB,EAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SACjD;QACD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QACvC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,uBAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,uBAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OACE,IAAI,CAAC,OAAO,CAAC,MAAM,CACjB,IAAI,aAAa,IAAI,eAAe,gBAAgB,KAAK,aAAa,QAAQ,EAAE,EAChF,OAAO,CAEV,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAuBD,GAAG,CACD,KAAa,EACb,QAAgB,EAChB,SAAgD,EAAE,EAClD,OAA6B;QAE7B,IAAI,IAAA,uBAAgB,EAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SAC9C;QACD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QACvC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,uBAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,uBAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OACE,IAAI,CAAC,OAAO,CAAC,GAAG,CACd,IAAI,aAAa,IAAI,eAAe,gBAAgB,KAAK,aAAa,QAAQ,EAAE,EAChF,OAAO,CAEV,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;CACF;AApQD,4BAoQC;AAED,MAAa,6BAA8B,SAAQ,uBAA8B;CAAG;AAApF,sEAAoF;AAu3CpF,QAAQ,CAAC,6BAA6B,GAAG,6BAA6B,CAAC"}