import { APIResource } from "../../../../resource.js";
import * as SummaryAPI from "./summary.js";
import { Summary, SummaryModelParams, SummaryModelResponse, SummaryTaskParams, SummaryTaskResponse } from "./summary.js";
import * as TimeseriesGroupsAPI from "./timeseries-groups/timeseries-groups.js";
import { TimeseriesGroups } from "./timeseries-groups/timeseries-groups.js";
export declare class Inference extends APIResource {
    summary: SummaryAPI.Summary;
    timeseriesGroups: TimeseriesGroupsAPI.TimeseriesGroups;
}
export declare namespace Inference {
    export { Summary as Summary, type SummaryModelResponse as SummaryModelResponse, type SummaryTaskResponse as SummaryTaskResponse, type SummaryModelParams as SummaryModelParams, type SummaryTaskParams as SummaryTaskParams, };
    export { TimeseriesGroups as TimeseriesGroups };
}
//# sourceMappingURL=inference.d.ts.map