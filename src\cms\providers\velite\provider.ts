/**
 * Velite CMS Provider
 * 
 * Implementation of the CMS provider interface for Veli<PERSON>.
 * This provider handles content retrieval from Velite-generated data.
 */

import { BaseCMSProvider } from '../base'
import type {
  ContentItem,
  ContentType,
  ContentQueryOptions,
  ContentAvailability,
  BlogContent,
  ProductContent,
  CaseStudyContent,
  VeliteConfig
} from '../../types'

export class VeliteProvider extends BaseCMSProvider {
  readonly name = 'velite'
  readonly version = '1.0.0'
  
  private veliteData: any = null
  
  constructor(config?: VeliteConfig) {
    super(config)
  }
  
  protected async doInitialize(): Promise<void> {
    try {
      // Import Velite generated data
      this.veliteData = await import('.velite')
      console.log('✅ Velite provider initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize Velite provider:', error)
      throw new Error('Velite data not found. Run "velite build" first.')
    }
  }
  
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    this.ensureInitialized()
    this.validateContentType(type)
    this.validateLocale(locale)
    
    const normalizedSlug = this.normalizeSlug(slug)
    const collection = this.getCollection(type)
    
    if (!collection) {
      return null
    }
    
    const content = collection.find((item: any) => 
      item.slug === normalizedSlug && item.lang === locale
    )
    
    return content || null
  }
  
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: ContentQueryOptions
  ): Promise<T[]> {
    this.ensureInitialized()
    this.validateContentType(type)
    this.validateLocale(locale)
    
    const collection = this.getCollection(type)
    
    if (!collection) {
      return []
    }
    
    let content = collection.filter((item: any) => item.lang === locale)
    
    // Apply filters
    if (options?.featured !== undefined) {
      content = content.filter((item: any) => item.featured === options.featured)
    }
    
    if (options?.tags && options.tags.length > 0) {
      content = content.filter((item: any) => 
        item.tags && options.tags!.some(tag => item.tags.includes(tag))
      )
    }
    
    // Apply sorting
    if (options?.sortBy) {
      content.sort((a: any, b: any) => {
        const aValue = a[options.sortBy!]
        const bValue = b[options.sortBy!]
        const order = options.order === 'desc' ? -1 : 1
        
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return aValue.localeCompare(bValue) * order
        }
        
        if (aValue instanceof Date && bValue instanceof Date) {
          return (aValue.getTime() - bValue.getTime()) * order
        }
        
        return 0
      })
    }
    
    // Apply limit
    if (options?.limit) {
      content = content.slice(0, options.limit)
    }
    
    return content
  }
  
  async getAvailableLanguageVersions(
    type: ContentType,
    slug: string
  ): Promise<ContentAvailability[]> {
    this.ensureInitialized()
    this.validateContentType(type)
    
    const normalizedSlug = this.normalizeSlug(slug)
    const collection = this.getCollection(type)
    
    if (!collection) {
      return []
    }
    
    const versions = collection
      .filter((item: any) => item.slug === normalizedSlug)
      .map((item: any) => ({
        lang: item.lang,
        title: item.title,
        url: item.url,
        available: true
      }))
    
    return versions
  }
  
  async getRelatedContent<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit: number = 3
  ): Promise<T[]> {
    this.ensureInitialized()
    this.validateContentType(type)
    this.validateLocale(locale)
    
    const normalizedSlug = this.normalizeSlug(currentSlug)
    const collection = this.getCollection(type)
    
    if (!collection) {
      return []
    }
    
    // Get current content to find related by tags
    const current = collection.find((item: any) => 
      item.slug === normalizedSlug && item.lang === locale
    )
    
    if (!current) {
      return []
    }
    
    const allContent = collection.filter((item: any) => item.lang === locale)
    
    // Find related content by tags
    let related = allContent
      .filter((item: any) => item.slug !== normalizedSlug)
      .filter((item: any) => {
        if (!current.tags || !item.tags) return false
        return current.tags.some((tag: string) => item.tags.includes(tag))
      })
      .slice(0, limit)
    
    // If not enough related content, fill with recent content
    if (related.length < limit) {
      const recent = allContent
        .filter((item: any) => item.slug !== normalizedSlug)
        .filter((item: any) => !related.includes(item))
        .sort((a: any, b: any) => {
          const dateA = new Date(a.publishedAt || a.createdAt).getTime()
          const dateB = new Date(b.publishedAt || b.createdAt).getTime()
          return dateB - dateA
        })
        .slice(0, limit - related.length)
      
      related = [...related, ...recent]
    }
    
    return related
  }
  
  /**
   * Get the appropriate collection based on content type
   */
  private getCollection(type: ContentType): any[] | null {
    if (!this.veliteData) {
      return null
    }
    
    switch (type) {
      case 'blog':
        return this.veliteData.blogs || []
      case 'product':
        return this.veliteData.products || []
      case 'case-study':
        return this.veliteData.caseStudies || []
      default:
        return null
    }
  }
}
