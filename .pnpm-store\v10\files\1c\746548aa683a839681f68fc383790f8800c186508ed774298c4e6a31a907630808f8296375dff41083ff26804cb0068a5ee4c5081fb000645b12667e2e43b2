"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Summary = void 0;
const resource_1 = require("../../../../resource.js");
const core_1 = require("../../../../core.js");
class Summary extends resource_1.APIResource {
    bitrate(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.bitrate({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/bitrate', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
    duration(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.duration({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/duration', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
    industry(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.industry({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/industry', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
    ipVersion(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.ipVersion({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/ip_version', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
    protocol(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.protocol({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/protocol', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
    vector(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.vector({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/vector', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
    vertical(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.vertical({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/vertical', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
}
exports.Summary = Summary;
//# sourceMappingURL=summary.js.map