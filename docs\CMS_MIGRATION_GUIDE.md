# CMS Migration Guide

## 🎯 Overview

This guide documents the migration from Contentlayer to a flexible CMS abstraction layer with <PERSON><PERSON><PERSON> as the initial provider. The new system supports multiple CMS backends while maintaining backward compatibility.

## 📋 Migration Summary

### What Changed
- ✅ **Contentlayer** → **Velite** (better performance, modern architecture)
- ✅ **Direct imports** → **CMS abstraction layer**
- ✅ **Synchronous APIs** → **Asynchronous APIs**
- ✅ **Basic SEO** → **Enhanced SEO with structured data**
- ✅ **Simple caching** → **Advanced caching system**

### What Stayed the Same
- ✅ **Content structure** (blogs, products, case studies)
- ✅ **File organization** (`content/` directory)
- ✅ **URL patterns** and routing
- ✅ **Component interfaces** (mostly backward compatible)

## 🔧 Technical Changes

### 1. Import Path Updates

**Before (Contentlayer):**
```typescript
import { allBlogs, allProducts, allCaseStudies } from 'contentlayer/generated'
```

**After (CMS Abstraction):**
```typescript
import { cms, initializeCMS } from '@/cms'
import type { BlogContent, ProductContent, CaseStudyContent } from '@/cms/types'
```

### 2. API Changes

**Before (Synchronous):**
```typescript
// Get all blogs
const blogs = allBlogs.filter(blog => blog.lang === locale)

// Get single blog
const blog = allBlogs.find(b => b.slug === slug && b.lang === locale)

// Check existence
const exists = allBlogs.some(blog => blog.slug === slug && blog.lang === locale)
```

**After (Asynchronous):**
```typescript
// Initialize CMS first
await initializeCMS()

// Get all blogs
const blogs = await cms.getContentList<BlogContent>('blog', locale)

// Get single blog
const blog = await cms.getContent<BlogContent>('blog', slug, locale)

// Check existence
const exists = await cms.contentExists('blog', slug, locale)
```

### 3. Component Updates

**Before:**
```typescript
// Direct Contentlayer usage
export async function generateStaticParams() {
  return allBlogs.map(blog => ({ 
    locale: blog.lang, 
    slug: blog.slug 
  }))
}
```

**After:**
```typescript
// CMS abstraction usage
export async function generateStaticParams() {
  await initializeCMS()
  
  const [enBlogs, zhBlogs] = await Promise.all([
    cms.getContentList<BlogContent>('blog', 'en'),
    cms.getContentList<BlogContent>('blog', 'zh')
  ])
  
  const allBlogs = [...enBlogs, ...zhBlogs]
  
  return allBlogs.map(blog => ({ 
    locale: blog.lang, 
    slug: blog.slug 
  }))
}
```

## 🏗️ New Architecture

### CMS Abstraction Layer
```
src/cms/
├── types/           # Unified type definitions
├── providers/       # CMS provider implementations
│   ├── base.ts     # Abstract base class
│   ├── velite/     # Velite provider (active)
│   ├── strapi/     # Strapi provider (placeholder)
│   └── sanity/     # Sanity provider (placeholder)
├── services/        # Business logic services
│   ├── content.ts  # Content operations
│   ├── cache.ts    # Caching layer
│   ├── seo.ts      # SEO utilities
│   └── factory.ts  # Service factory
├── cms.ts          # Main CMS class
└── index.ts        # Public API
```

### Service Layer Benefits
- **Content Service**: Unified content operations
- **Cache Service**: Intelligent caching with TTL
- **SEO Service**: Enhanced SEO with structured data
- **Factory Pattern**: Easy service management

## 🚀 Enhanced Features

### 1. Advanced SEO
```typescript
// Automatic structured data generation
const seoService = cms.getSEOService()
const structuredData = seoService.generateStructuredData(content)
const metaTags = seoService.generateMetaTags(content)

// Multi-language sitemap with hreflang
const sitemap = seoService.generateSitemap(allContent)

// RSS feeds for each language
const rssFeeds = seoService.generateRSSFeeds(blogs)
```

### 2. Intelligent Caching
```typescript
// Automatic caching with TTL
const cacheService = cms.getCacheService()

// Cache content automatically
const content = await cms.getContent('blog', slug, locale) // Cached automatically

// Manual cache control
cacheService.invalidate('blog', slug, locale)
cacheService.clear()
```

### 3. Enhanced Components
```typescript
// Optimized image components
import { ContentCoverImage, AuthorImage } from '@/components/content'

// SEO components
import { ContentStructuredData, ContentMetaTags } from '@/components/content'

// Usage
<ContentCoverImage image={blog.coverImage} title={blog.title} priority />
<ContentStructuredData content={blog} />
<ContentMetaTags content={blog} />
```

## 📦 Build System Changes

### Package.json Updates
```json
{
  "dependencies": {
    "velite": "^0.2.4",
    "zod": "^3.24.1"
  },
  "scripts": {
    "build": "velite build && pnpm generate:content && next build",
    "build:content": "velite build",
    "build:content:watch": "velite build --watch",
    "test:cms": "tsx scripts/test-cms.ts"
  }
}
```

### Configuration Files
- ✅ `velite.config.ts` - Velite configuration
- ✅ `src/cms/config.ts` - CMS system configuration
- ✅ Updated `next.config.mjs` - Velite integration
- ✅ Updated `.gitignore` - `.velite` instead of `.contentlayer`

## 🔄 Migration Steps

### Step 1: Install Dependencies
```bash
pnpm add velite zod
pnpm remove contentlayer next-contentlayer
```

### Step 2: Update Configuration
```bash
# Remove old config
rm contentlayer.config.ts

# New configs are already in place:
# - velite.config.ts
# - src/cms/config.ts
```

### Step 3: Update Imports
Replace all Contentlayer imports with CMS imports:
```typescript
// Old
import { allBlogs } from 'contentlayer/generated'

// New
import { cms, initializeCMS } from '@/cms'
```

### Step 4: Update API Calls
Convert synchronous calls to asynchronous:
```typescript
// Add await and proper initialization
await initializeCMS()
const content = await cms.getContent(...)
```

### Step 5: Test the System
```bash
# Test CMS functionality
pnpm test:cms

# Build content
pnpm build:content

# Generate SEO files
pnpm generate:content
```

## 🎯 Future CMS Migration

The abstraction layer makes it easy to switch to other CMS providers:

### Strapi Migration
```typescript
// Just change the provider configuration
const cmsConfig = {
  provider: {
    name: 'strapi',
    config: {
      apiUrl: 'https://your-strapi.com/api',
      token: 'your-api-token'
    }
  }
}
```

### Sanity Migration
```typescript
const cmsConfig = {
  provider: {
    name: 'sanity',
    config: {
      projectId: 'your-project-id',
      dataset: 'production',
      apiVersion: '2023-01-01'
    }
  }
}
```

## 🐛 Troubleshooting

### Common Issues

1. **"CMS not initialized" error**
   ```typescript
   // Always initialize before using
   await initializeCMS()
   ```

2. **Velite build errors**
   ```bash
   # Clear cache and rebuild
   rm -rf .velite
   pnpm build:content
   ```

3. **Type errors**
   ```typescript
   // Use proper types
   import type { BlogContent } from '@/cms/types'
   const blog = await cms.getContent<BlogContent>('blog', slug, locale)
   ```

4. **Async/await issues**
   ```typescript
   // Convert all content operations to async
   const handleLanguageSwitch = async (locale: string) => {
     const exists = await cms.contentExists(type, slug, locale)
   }
   ```

## ✅ Verification Checklist

- [ ] All Contentlayer imports removed
- [ ] CMS system initializes correctly
- [ ] Content retrieval works
- [ ] SEO generation works
- [ ] Build process completes
- [ ] All pages render correctly
- [ ] Language switching works
- [ ] Cache system functions
- [ ] Tests pass

## 📚 Additional Resources

- [Velite Documentation](https://velite.js.org/)
- [CMS Types Reference](./src/cms/types/index.ts)
- [Provider Implementation Guide](./src/cms/providers/README.md)
- [SEO Service Guide](./src/cms/services/README.md)
