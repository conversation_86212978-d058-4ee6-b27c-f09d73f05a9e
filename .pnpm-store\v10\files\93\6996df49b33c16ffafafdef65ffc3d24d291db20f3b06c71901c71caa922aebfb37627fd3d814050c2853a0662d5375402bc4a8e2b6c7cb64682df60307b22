// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import * as TargetsAPI from "./targets.mjs";
import { TargetBulkUpdateResponsesSinglePage, TargetListResponsesV4PagePaginationArray, Targets, } from "./targets.mjs";
export class Infrastructure extends APIResource {
    constructor() {
        super(...arguments);
        this.targets = new TargetsAPI.Targets(this._client);
    }
}
Infrastructure.Targets = Targets;
Infrastructure.TargetListResponsesV4PagePaginationArray = TargetListResponsesV4PagePaginationArray;
Infrastructure.TargetBulkUpdateResponsesSinglePage = TargetBulkUpdateResponsesSinglePage;
//# sourceMappingURL=infrastructure.mjs.map