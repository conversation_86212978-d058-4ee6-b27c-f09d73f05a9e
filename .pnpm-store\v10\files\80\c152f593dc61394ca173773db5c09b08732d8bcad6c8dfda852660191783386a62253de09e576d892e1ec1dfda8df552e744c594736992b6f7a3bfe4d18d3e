{"version": 3, "file": "devices.d.ts", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/dex/fleet-status/devices.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,qBAAqB,EAAE,KAAK,2BAA2B,EAAE,MAAM,wBAAwB,CAAC;AAEjG,qBAAa,OAAQ,SAAQ,WAAW;IACtC;;;;;;;;;;;;;;;;;;OAkBG;IACH,IAAI,CACF,MAAM,EAAE,gBAAgB,EACxB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,WAAW,CAAC,wCAAwC,EAAE,kBAAkB,CAAC;CAQlF;AAED,qBAAa,wCAAyC,SAAQ,qBAAqB,CAAC,kBAAkB,CAAC;CAAG;AAE1G,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAE1B,eAAe,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAEjC,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE9B,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE3B,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE/B,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEvB,WAAW,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC;IAElE,UAAU,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC;IAE3C,UAAU,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC;IAE3C;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5B,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE7B,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE7B,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE7B,gBAAgB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEjC,eAAe,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAEjC,WAAW,CAAC,EAAE,kBAAkB,CAAC,WAAW,CAAC;IAE7C,WAAW,CAAC,EAAE,kBAAkB,CAAC,WAAW,CAAC;IAE7C,kBAAkB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEnC,OAAO,CAAC,EAAE,kBAAkB,CAAC,OAAO,CAAC;IAErC,OAAO,CAAC,EAAE,kBAAkB,CAAC,OAAO,CAAC;IAErC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEtB,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE/B,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE/B,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5B;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE/B,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE3B,eAAe,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,GAAG,IAAI,CAAC;IAE1E,YAAY,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAE9B,eAAe,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACjC;AAED,yBAAiB,kBAAkB,CAAC;IAClC,UAAiB,WAAW;QAC1B,OAAO,CAAC,EAAE,MAAM,CAAC;QAEjB,IAAI,CAAC,EAAE,MAAM,CAAC;KACf;IAED,UAAiB,UAAU;QACzB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAExB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAEpB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAEpB,QAAQ,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC;QAE/B,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAExB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,UAAU,CAAC;QAC1B,UAAiB,QAAQ;YACvB,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAErB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAE5B,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAE1B,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SACrB;KACF;IAED,UAAiB,UAAU;QACzB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAExB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAEpB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAEpB,QAAQ,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC;QAE/B,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAExB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,UAAU,CAAC;QAC1B,UAAiB,QAAQ;YACvB,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAErB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAE5B,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAE1B,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SACrB;KACF;IAED,UAAiB,WAAW;QAC1B,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAExB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAEpB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAEpB,QAAQ,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC;QAEhC,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAExB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,WAAW,CAAC;QAC3B,UAAiB,QAAQ;YACvB,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAErB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAE5B,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAE1B,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SACrB;KACF;IAED,UAAiB,WAAW;QAC1B,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAExB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAEpB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAEpB,QAAQ,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC;QAEhC,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAExB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,WAAW,CAAC;QAC3B,UAAiB,QAAQ;YACvB,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAErB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAE5B,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAE1B,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SACrB;KACF;IAED,UAAiB,OAAO;QACtB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAExB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAEpB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAEpB,QAAQ,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC;QAE5B,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAExB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,OAAO,CAAC;QACvB,UAAiB,QAAQ;YACvB,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAErB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAE5B,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAE1B,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SACrB;KACF;IAED,UAAiB,OAAO;QACtB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAExB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAEpB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAEpB,QAAQ,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC;QAE5B,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QAExB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,OAAO,CAAC;QACvB,UAAiB,QAAQ;YACvB,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAErB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAE5B,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;YAE1B,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SACrB;KACF;IAED,UAAiB,eAAe;QAC9B,IAAI,CAAC,EAAE,MAAM,CAAC;QAEd,YAAY,CAAC,EAAE,MAAM,CAAC;KACvB;CACF;AAED,MAAM,WAAW,gBAAiB,SAAQ,2BAA2B;IACnE;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,WAAW,GAAG,MAAM,GAAG,UAAU,GAAG,QAAQ,GAAG,WAAW,GAAG,SAAS,CAAC;IAE1F;;;;;;OAMG;IACH,MAAM,CAAC,EAAE,WAAW,GAAG,QAAQ,GAAG,KAAK,CAAC;IAExC;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAID,MAAM,CAAC,OAAO,WAAW,OAAO,CAAC;IAC/B,OAAO,EACL,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,wCAAwC,IAAI,wCAAwC,EACpF,KAAK,gBAAgB,IAAI,gBAAgB,GAC1C,CAAC;CACH"}