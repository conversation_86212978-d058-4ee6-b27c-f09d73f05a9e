/**
 * CMS Configuration Type Definitions
 * 
 * Configuration interfaces for CMS providers and system settings.
 */

import type { ContentType } from './content'

// Main CMS configuration interface
export interface CMSConfig {
  // Active provider configuration
  provider: {
    name: string
    config?: Record<string, any>
  }
  
  // Content type configurations
  contentTypes: Record<ContentType, ContentTypeConfig>
  
  // Cache configuration
  cache: {
    enabled: boolean
    ttl: number
    strategy: 'memory' | 'redis' | 'file'
  }
  
  // SEO configuration
  seo: {
    sitemap: SitemapConfig
    rss: RSSConfig
    structuredData: StructuredDataConfig
  }
  
  // Development configuration
  development: {
    watch: boolean
    hotReload: boolean
    validation: boolean
  }
}

// Content type configuration
export interface ContentTypeConfig {
  fields: string[]
  required: string[]
  multilingual: boolean
  seo: {
    enabled: boolean
    structuredDataType?: string
  }
}

// Sitemap configuration
export interface SitemapConfig {
  enabled: boolean
  filename: string
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: {
    default: number
    featured: number
    static: number
  }
  multilingual: {
    enabled: boolean
    hreflang: boolean
  }
}

// RSS configuration
export interface RSSConfig {
  enabled: boolean
  limit: number
  multilingual: boolean
  feeds: Record<string, RSSFeedConfig>
}

// RSS feed configuration
export interface RSSFeedConfig {
  filename: string
  title: string
  description: string
  language: string
  contentTypes: ContentType[]
}

// Structured data configuration
export interface StructuredDataConfig {
  enabled: boolean
  types: {
    blog: boolean
    product: boolean
    caseStudy: boolean
    organization: boolean
    breadcrumb: boolean
  }
}

// Provider-specific configuration interfaces
export interface VeliteConfig {
  root: string
  output: {
    data: string
    assets: string
    base: string
    name: string
    clean: boolean
  }
  cache: {
    enabled: boolean
    dir: string
  }
  concurrency: number
  development: {
    skipImageOptimization: boolean
    concurrency: number
  }
}

// Placeholder for future CMS configurations
export interface StrapiConfig {
  apiUrl: string
  apiToken: string
  // Additional Strapi-specific configuration
}

export interface SanityConfig {
  projectId: string
  dataset: string
  apiVersion: string
  // Additional Sanity-specific configuration
}

export interface ContentfulConfig {
  spaceId: string
  accessToken: string
  environment: string
  // Additional Contentful-specific configuration
}
