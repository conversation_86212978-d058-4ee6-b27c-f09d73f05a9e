/**
 * Enhanced Meta Tags Component
 * 
 * React component for rendering comprehensive meta tags for SEO.
 * Supports Open Graph, Twitter Cards, and multi-language alternates.
 */

import { cms } from '@/cms'
import type { ContentItem, MetaTags } from '@/cms/types'

interface MetaTagsProps {
  metaTags: MetaTags
}

/**
 * Meta Tags Component
 * 
 * Renders comprehensive meta tags for SEO including Open Graph,
 * Twitter Cards, and language alternates.
 */
export function MetaTagsComponent({ metaTags }: MetaTagsProps) {
  return (
    <>
      {/* Basic Meta Tags */}
      <title>{metaTags.title}</title>
      <meta name="description" content={metaTags.description} />
      {metaTags.canonical && (
        <link rel="canonical" href={metaTags.canonical} />
      )}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={metaTags.openGraph.title} />
      <meta property="og:description" content={metaTags.openGraph.description} />
      <meta property="og:type" content={metaTags.openGraph.type} />
      <meta property="og:url" content={metaTags.openGraph.url} />
      
      {metaTags.openGraph.images && metaTags.openGraph.images.map((image, index) => (
        <div key={index}>
          <meta property="og:image" content={image.url} />
          <meta property="og:image:width" content={image.width.toString()} />
          <meta property="og:image:height" content={image.height.toString()} />
          <meta property="og:image:alt" content={image.alt} />
        </div>
      ))}
      
      {metaTags.openGraph.publishedTime && (
        <meta property="article:published_time" content={metaTags.openGraph.publishedTime} />
      )}
      
      {metaTags.openGraph.modifiedTime && (
        <meta property="article:modified_time" content={metaTags.openGraph.modifiedTime} />
      )}
      
      {metaTags.openGraph.authors && metaTags.openGraph.authors.map((author, index) => (
        <meta key={index} property="article:author" content={author} />
      ))}
      
      {metaTags.openGraph.tags && metaTags.openGraph.tags.map((tag, index) => (
        <meta key={index} property="article:tag" content={tag} />
      ))}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={metaTags.twitter.card} />
      <meta name="twitter:title" content={metaTags.twitter.title} />
      <meta name="twitter:description" content={metaTags.twitter.description} />
      
      {metaTags.twitter.images && metaTags.twitter.images.map((image, index) => (
        <meta key={index} name="twitter:image" content={image} />
      ))}
      
      {metaTags.twitter.creator && (
        <meta name="twitter:creator" content={metaTags.twitter.creator} />
      )}
      
      {metaTags.twitter.site && (
        <meta name="twitter:site" content={metaTags.twitter.site} />
      )}
      
      {/* Language Alternates */}
      {metaTags.alternates && metaTags.alternates.map((alternate, index) => (
        <link
          key={index}
          rel="alternate"
          hrefLang={alternate.hreflang}
          href={alternate.href}
        />
      ))}
    </>
  )
}

/**
 * Content Meta Tags Component
 * 
 * Automatically generates and renders meta tags for content items.
 * Uses the CMS SEO service to generate appropriate meta tags.
 */
interface ContentMetaTagsProps {
  content: ContentItem
}

export function ContentMetaTags({ content }: ContentMetaTagsProps) {
  const seoService = cms.getSEOService()
  const metaTags = seoService.generateMetaTags(content)
  
  return <MetaTagsComponent metaTags={metaTags} />
}

/**
 * Page Meta Tags Component
 * 
 * Renders meta tags for static pages with custom metadata.
 */
interface PageMetaTagsProps {
  title: string
  description: string
  url: string
  image?: {
    url: string
    width: number
    height: number
    alt: string
  }
  type?: 'website' | 'article' | 'product'
}

export function PageMetaTags({
  title,
  description,
  url,
  image,
  type = 'website'
}: PageMetaTagsProps) {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://shipany.ai'
  const canonicalUrl = `${baseUrl}${url}`
  
  const metaTags: MetaTags = {
    title,
    description,
    canonical: canonicalUrl,
    openGraph: {
      title,
      description,
      type,
      url: canonicalUrl,
      ...(image && {
        images: [{
          url: image.url,
          width: image.width,
          height: image.height,
          alt: image.alt
        }]
      })
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      ...(image && {
        images: [image.url]
      })
    }
  }
  
  return <MetaTagsComponent metaTags={metaTags} />
}

// Export all components
export {
  MetaTagsComponent as MetaTags,
  PageMetaTags
}
