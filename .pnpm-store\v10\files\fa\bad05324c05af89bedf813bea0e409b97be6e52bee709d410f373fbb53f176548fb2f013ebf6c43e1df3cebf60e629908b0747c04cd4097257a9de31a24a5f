import { APIResource } from "../../../../resource.js";
import * as NetworkPathAPI from "./network-path.js";
import { NetworkPath, NetworkPathGetParams, NetworkPathGetResponse } from "./network-path.js";
export declare class TracerouteTestResults extends APIResource {
    networkPath: NetworkPathAPI.NetworkPath;
}
export declare namespace TracerouteTestResults {
    export { NetworkPath as NetworkPath, type NetworkPathGetResponse as NetworkPathGetResponse, type NetworkPathGetParams as NetworkPathGetParams, };
}
//# sourceMappingURL=traceroute-test-results.d.ts.map