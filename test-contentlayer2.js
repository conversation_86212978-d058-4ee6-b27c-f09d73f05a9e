// Test script to verify Contentlayer2 compatibility with Next.js 15 + React 19
const { execSync } = require('child_process');

console.log('Testing Contentlayer2 compatibility...\n');

// Check current versions
try {
  const nextVersion = execSync('pnpm list next --depth=0', { encoding: 'utf8' });
  const reactVersion = execSync('pnpm list react --depth=0', { encoding: 'utf8' });
  
  console.log('Current versions:');
  console.log(nextVersion);
  console.log(reactVersion);
  
  // Check if contentlayer2 is available
  const contentlayer2Info = execSync('pnpm view contentlayer2 --json', { encoding: 'utf8' });
  const packageInfo = JSON.parse(contentlayer2Info);
  
  console.log('\nContentlayer2 package info:');
  console.log(`Version: ${packageInfo.version}`);
  console.log(`Last modified: ${packageInfo.time.modified}`);
  console.log(`Dependencies:`, Object.keys(packageInfo.dependencies || {}).slice(0, 10));
  
} catch (error) {
  console.error('Error checking versions:', error.message);
}
