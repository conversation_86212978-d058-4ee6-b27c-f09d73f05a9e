import { APIResource } from "../../../../resource.js";
import * as Core from "../../../../core.js";
import * as SummaryAPI from "./summary.js";
import { Summary, SummaryBitrateParams, SummaryBitrateResponse, SummaryDurationParams, SummaryDurationResponse, SummaryIPVersionParams, SummaryIPVersionResponse, SummaryIndustryParams, SummaryIndustryResponse, SummaryProtocolParams, SummaryProtocolResponse, SummaryVectorParams, SummaryVectorResponse, SummaryVerticalParams, SummaryVerticalResponse } from "./summary.js";
import * as TimeseriesGroupsAPI from "./timeseries-groups.js";
import { TimeseriesGroupBitrateParams, TimeseriesGroupBitrateResponse, TimeseriesGroupDurationParams, TimeseriesGroupDurationResponse, TimeseriesGroupIPVersionParams, TimeseriesGroupIPVersionResponse, TimeseriesGroupIndustryParams, TimeseriesGroupIndustryResponse, TimeseriesGroupProtocolParams, TimeseriesGroupProtocolResponse, TimeseriesGroupVectorParams, TimeseriesGroupVectorResponse, TimeseriesGroupVerticalParams, TimeseriesGroupVerticalResponse, TimeseriesGroups } from "./timeseries-groups.js";
import * as TopAPI from "./top/top.js";
import { Top, TopAttacksParams, TopAttacksResponse, TopIndustryParams, TopIndustryResponse, TopVerticalParams, TopVerticalResponse } from "./top/top.js";
export declare class Layer3 extends APIResource {
    summary: SummaryAPI.Summary;
    timeseriesGroups: TimeseriesGroupsAPI.TimeseriesGroups;
    top: TopAPI.Top;
    /**
     * Retrieves layer 3 attacks over time.
     *
     * @example
     * ```ts
     * const response =
     *   await client.radar.attacks.layer3.timeseries();
     * ```
     */
    timeseries(query?: Layer3TimeseriesParams, options?: Core.RequestOptions): Core.APIPromise<Layer3TimeseriesResponse>;
    timeseries(options?: Core.RequestOptions): Core.APIPromise<Layer3TimeseriesResponse>;
}
export interface Layer3TimeseriesResponse {
    /**
     * Metadata for the results.
     */
    meta: Layer3TimeseriesResponse.Meta;
    [k: string]: Layer3TimeseriesResponse.unnamed_schema_ref_75bae70cf28e6bcef364b9840db3bdeb | Layer3TimeseriesResponse.Meta | undefined;
}
export declare namespace Layer3TimeseriesResponse {
    /**
     * Metadata for the results.
     */
    interface Meta {
        /**
         * Aggregation interval of the results (e.g., in 15 minutes or 1 hour intervals).
         * Refer to
         * [Aggregation intervals](https://developers.cloudflare.com/radar/concepts/aggregation-intervals/).
         */
        aggInterval: 'FIFTEEN_MINUTES' | 'ONE_HOUR' | 'ONE_DAY' | 'ONE_WEEK' | 'ONE_MONTH';
        confidenceInfo: Meta.ConfidenceInfo;
        dateRange: Array<Meta.DateRange>;
        /**
         * Timestamp of the last dataset update.
         */
        lastUpdated: string;
        /**
         * Normalization method applied to the results. Refer to
         * [Normalization methods](https://developers.cloudflare.com/radar/concepts/normalization/).
         */
        normalization: 'PERCENTAGE' | 'MIN0_MAX' | 'MIN_MAX' | 'RAW_VALUES' | 'PERCENTAGE_CHANGE' | 'ROLLING_AVERAGE' | 'OVERLAPPED_PERCENTAGE' | 'RATIO';
        /**
         * Measurement units for the results.
         */
        units: Array<Meta.Unit>;
    }
    namespace Meta {
        interface ConfidenceInfo {
            annotations: Array<ConfidenceInfo.Annotation>;
            /**
             * Provides an indication of how much confidence Cloudflare has in the data.
             */
            level: number;
        }
        namespace ConfidenceInfo {
            /**
             * Annotation associated with the result (e.g. outage or other type of event).
             */
            interface Annotation {
                dataSource: string;
                description: string;
                endDate: string;
                eventType: string;
                /**
                 * Whether event is a single point in time or a time range.
                 */
                isInstantaneous: boolean;
                linkedUrl: string;
                startDate: string;
            }
        }
        interface DateRange {
            /**
             * Adjusted end of date range.
             */
            endTime: string;
            /**
             * Adjusted start of date range.
             */
            startTime: string;
        }
        interface Unit {
            name: string;
            value: string;
        }
    }
    interface unnamed_schema_ref_75bae70cf28e6bcef364b9840db3bdeb {
        timestamps: Array<string>;
        values: Array<string>;
    }
}
export interface Layer3TimeseriesParams {
    /**
     * Aggregation interval of the results (e.g., in 15 minutes or 1 hour intervals).
     * Refer to
     * [Aggregation intervals](https://developers.cloudflare.com/radar/concepts/aggregation-intervals/).
     */
    aggInterval?: '15m' | '1h' | '1d' | '1w';
    /**
     * Filters results by Autonomous System. Specify one or more Autonomous System
     * Numbers (ASNs) as a comma-separated list. Prefix with `-` to exclude ASNs from
     * results. For example, `-174, 3356` excludes results from AS174, but includes
     * results from AS3356.
     */
    asn?: Array<string>;
    /**
     * Filters results by continent. Specify a comma-separated list of alpha-2 codes.
     * Prefix with `-` to exclude continents from results. For example, `-EU,NA`
     * excludes results from EU, but includes results from NA.
     */
    continent?: Array<string>;
    /**
     * End of the date range (inclusive).
     */
    dateEnd?: Array<string>;
    /**
     * Filters results by date range. For example, use `7d` and `7dcontrol` to compare
     * this week with the previous week. Use this parameter or set specific start and
     * end dates (`dateStart` and `dateEnd` parameters).
     */
    dateRange?: Array<string>;
    /**
     * Start of the date range.
     */
    dateStart?: Array<string>;
    /**
     * Specifies whether the `location` filter applies to the source or target
     * location.
     */
    direction?: 'ORIGIN' | 'TARGET';
    /**
     * Format in which results will be returned.
     */
    format?: 'JSON' | 'CSV';
    /**
     * Filters results by IP version (Ipv4 vs. IPv6).
     */
    ipVersion?: Array<'IPv4' | 'IPv6'>;
    /**
     * Filters results by location. Specify a comma-separated list of alpha-2 codes.
     * Prefix with `-` to exclude locations from results. For example, `-US,PT`
     * excludes results from the US, but includes results from PT.
     */
    location?: Array<string>;
    /**
     * Measurement units, eg. bytes.
     */
    metric?: 'BYTES' | 'BYTES_OLD';
    /**
     * Array of names used to label the series in the response.
     */
    name?: Array<string>;
    /**
     * Normalization method applied to the results. Refer to
     * [Normalization methods](https://developers.cloudflare.com/radar/concepts/normalization/).
     */
    normalization?: 'PERCENTAGE_CHANGE' | 'MIN0_MAX';
    /**
     * Filters the results by layer 3/4 protocol.
     */
    protocol?: Array<'UDP' | 'TCP' | 'ICMP' | 'GRE'>;
}
export declare namespace Layer3 {
    export { type Layer3TimeseriesResponse as Layer3TimeseriesResponse, type Layer3TimeseriesParams as Layer3TimeseriesParams, };
    export { Summary as Summary, type SummaryBitrateResponse as SummaryBitrateResponse, type SummaryDurationResponse as SummaryDurationResponse, type SummaryIndustryResponse as SummaryIndustryResponse, type SummaryIPVersionResponse as SummaryIPVersionResponse, type SummaryProtocolResponse as SummaryProtocolResponse, type SummaryVectorResponse as SummaryVectorResponse, type SummaryVerticalResponse as SummaryVerticalResponse, type SummaryBitrateParams as SummaryBitrateParams, type SummaryDurationParams as SummaryDurationParams, type SummaryIndustryParams as SummaryIndustryParams, type SummaryIPVersionParams as SummaryIPVersionParams, type SummaryProtocolParams as SummaryProtocolParams, type SummaryVectorParams as SummaryVectorParams, type SummaryVerticalParams as SummaryVerticalParams, };
    export { TimeseriesGroups as TimeseriesGroups, type TimeseriesGroupBitrateResponse as TimeseriesGroupBitrateResponse, type TimeseriesGroupDurationResponse as TimeseriesGroupDurationResponse, type TimeseriesGroupIndustryResponse as TimeseriesGroupIndustryResponse, type TimeseriesGroupIPVersionResponse as TimeseriesGroupIPVersionResponse, type TimeseriesGroupProtocolResponse as TimeseriesGroupProtocolResponse, type TimeseriesGroupVectorResponse as TimeseriesGroupVectorResponse, type TimeseriesGroupVerticalResponse as TimeseriesGroupVerticalResponse, type TimeseriesGroupBitrateParams as TimeseriesGroupBitrateParams, type TimeseriesGroupDurationParams as TimeseriesGroupDurationParams, type TimeseriesGroupIndustryParams as TimeseriesGroupIndustryParams, type TimeseriesGroupIPVersionParams as TimeseriesGroupIPVersionParams, type TimeseriesGroupProtocolParams as TimeseriesGroupProtocolParams, type TimeseriesGroupVectorParams as TimeseriesGroupVectorParams, type TimeseriesGroupVerticalParams as TimeseriesGroupVerticalParams, };
    export { Top as Top, type TopAttacksResponse as TopAttacksResponse, type TopIndustryResponse as TopIndustryResponse, type TopVerticalResponse as TopVerticalResponse, type TopAttacksParams as TopAttacksParams, type TopIndustryParams as TopIndustryParams, type TopVerticalParams as TopVerticalParams, };
}
//# sourceMappingURL=layer3.d.ts.map