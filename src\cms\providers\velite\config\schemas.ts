/**
 * Velite Schema Definitions
 * 
 * This file defines the content schemas for <PERSON><PERSON><PERSON> using <PERSON><PERSON> for validation.
 * These schemas ensure type safety and data consistency across all content types.
 */

import { s } from 'velite'

/**
 * Base schema for all content types
 * 
 * This schema defines common fields that all content types share,
 * including metadata, SEO fields, and computed properties.
 */
const baseContentSchema = s.object({
  // Required fields
  title: s.string().max(100),
  slug: s.string(),
  
  // SEO and metadata fields
  description: s.string().max(200).optional(),
  coverImage: s.image().optional(),
  
  // Author information
  author: s.string().optional(),
  authorImage: s.image().optional(),
  
  // Publishing and organization
  publishedAt: s.isodate().optional(),
  updatedAt: s.isodate().optional(),
  featured: s.boolean().default(false),
  tags: s.array(s.string()).optional(),
  
  // Video content support
  videoUrl: s.string().optional(),
  videoThumbnail: s.image().optional(),
  videoDuration: s.string().optional(),
  
  // Computed fields
  lang: s.string().transform((_, { meta }) => {
    // Extract language from file path: blogs/en/post.mdx -> 'en'
    const pathParts = meta.path.split('/')
    return pathParts[pathParts.length - 3] || 'en'
  }),
  
  url: s.string().transform((_, { meta, data }) => {
    const pathParts = meta.path.split('/')
    const contentType = pathParts[0] // blogs, products, case-studies
    const lang = data.lang
    const slug = data.slug
    
    // Generate URL: /blogs/en/slug or /zh/blogs/slug
    return lang === 'en' 
      ? `/${contentType}/${slug}`
      : `/${lang}/${contentType}/${slug}`
  }),
  
  // Content processing
  content: s.mdx(),
  excerpt: s.excerpt({ length: 160 }),
  metadata: s.metadata(), // Reading time, word count, etc.
  
  // File metadata
  createdAt: s.isodate().transform((_, { meta }) => {
    // Get file creation time as fallback
    try {
      const fs = require('fs')
      const path = require('path')
      const filePath = path.join('content', meta.path)
      return fs.statSync(filePath).birthtime.toISOString()
    } catch {
      return new Date().toISOString()
    }
  })
})

/**
 * Blog schema
 * 
 * Schema for blog posts with all the base fields.
 * Can be extended with blog-specific fields if needed.
 */
export const blogSchema = baseContentSchema

/**
 * Product schema
 * 
 * Schema for product pages with additional product-specific fields.
 */
export const productSchema = baseContentSchema.extend({
  price: s.string().optional(),
  category: s.string().optional(),
  // Product-specific video duration field
  videoDuration: s.string().optional()
})

/**
 * Case study schema
 * 
 * Schema for case studies with client and industry information.
 */
export const caseStudySchema = baseContentSchema.extend({
  client: s.string().optional(),
  industry: s.string().optional(),
  results: s.array(s.string()).optional()
})
