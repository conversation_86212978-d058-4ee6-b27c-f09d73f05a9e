/**
 * Velite Schema Definitions
 * 
 * This file defines the content schemas for <PERSON><PERSON><PERSON> using <PERSON><PERSON> for validation.
 * These schemas ensure type safety and data consistency across all content types.
 */

import { s } from 'velite'
import { statSync } from 'fs'
import { join } from 'path'

/**
 * Base schema for all content types
 * 
 * This schema defines common fields that all content types share,
 * including metadata, SEO fields, and computed properties.
 */
const baseContentSchema = s.object({
  // Required fields
  title: s.string().max(100),
  slug: s.string(),
  
  // SEO and metadata fields
  description: s.string().max(200).optional(),
  coverImage: s.string().optional(), // Use string for external URLs

  // Author information
  author: s.string().optional(),
  authorImage: s.string().optional(), // Use string for external URLs
  
  // Publishing and organization
  publishedAt: s.isodate().optional(),
  updatedAt: s.isodate().optional(),
  featured: s.boolean().default(false),
  tags: s.array(s.string()).optional(),
  
  // Video content support
  videoUrl: s.string().optional(),
  videoThumbnail: s.string().optional(), // Use string for external URLs
  videoDuration: s.string().optional(),
  
  // Computed fields
  lang: s.string().default('en').transform((value, { meta }) => {
    // Extract language from file path: blogs/en/post.mdx -> 'en'
    const pathParts = meta.path.split('/')
    return pathParts[1] || 'en' // blogs/en/post.mdx -> 'en'
  }),

  url: s.string().default('').transform((value, { meta }) => {
    const pathParts = meta.path.split('/')
    const contentType = pathParts[0] // blogs, products, case-studies
    const lang = pathParts[1] || 'en' // en or zh
    const fileName = pathParts[2] || '' // post.mdx
    const slug = fileName.replace(/\.mdx?$/, '') // remove extension

    // Generate URL: /blogs/slug or /zh/blogs/slug
    return lang === 'en'
      ? `/${contentType}/${slug}`
      : `/${lang}/${contentType}/${slug}`
  }),
  
  // Content processing
  content: s.mdx(),
  excerpt: s.excerpt({ length: 160 }),
  metadata: s.metadata(), // Reading time, word count, etc.
  
  // File metadata
  createdAt: s.string().default('').transform((value, { meta }) => {
    // Generate a consistent date based on file path
    return new Date().toISOString()
  })
})

/**
 * Blog schema
 * 
 * Schema for blog posts with all the base fields.
 * Can be extended with blog-specific fields if needed.
 */
export const blogSchema = baseContentSchema

/**
 * Product schema
 * 
 * Schema for product pages with additional product-specific fields.
 */
export const productSchema = baseContentSchema.extend({
  price: s.string().optional(),
  category: s.string().optional(),
  // Product-specific video duration field
  videoDuration: s.string().optional()
})

/**
 * Case study schema
 * 
 * Schema for case studies with client and industry information.
 */
export const caseStudySchema = baseContentSchema.extend({
  client: s.string().optional(),
  industry: s.string().optional(),
  results: s.array(s.string()).optional()
})
