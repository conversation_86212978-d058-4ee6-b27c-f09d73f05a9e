"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Rules = exports.RuleListResponsesSinglePage = exports.Email = exports.AccountMapping = void 0;
var account_mapping_1 = require("./account-mapping.js");
Object.defineProperty(exports, "AccountMapping", { enumerable: true, get: function () { return account_mapping_1.AccountMapping; } });
var email_1 = require("./email.js");
Object.defineProperty(exports, "Email", { enumerable: true, get: function () { return email_1.Email; } });
var rules_1 = require("./rules.js");
Object.defineProperty(exports, "RuleListResponsesSinglePage", { enumerable: true, get: function () { return rules_1.RuleListResponsesSinglePage; } });
Object.defineProperty(exports, "Rules", { enumerable: true, get: function () { return rules_1.Rules; } });
//# sourceMappingURL=index.js.map