
export default [
  {
    "title": "English Only Test Article",
    "slug": "english-only-test",
    "description": "This article is only available in English to test the language switching fallback functionality.",
    "coverImage": "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=400&fit=crop",
    "author": "Test Author",
    "authorImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    "publishedAt": new Date("2025-01-17T00:00:00.000Z"),
    "featured": false,
    "tags": [
      "test",
      "english-only",
      "language-switching"
    ],
    "keywords": [],
    "content": "# English Only Test Article\r\n\r\nThis article is specifically created to test the language switching functionality when content is not available in all languages.\r\n\r\n## Purpose\r\n\r\nWhen a user tries to switch to Chinese (中文) while viewing this article, they should be redirected to the Chinese blog list page since this article doesn't have a Chinese version.\r\n\r\n## Testing Scenarios\r\n\r\n1. **Direct Language Switch**: Use the header language selector\r\n2. **Language Versions Indicator**: Use the language versions component on this page\r\n3. **Fallback Behavior**: Verify that users are redirected appropriately\r\n\r\n## Expected Behavior\r\n\r\n- ✅ English version should be accessible\r\n- ❌ Chinese version should not exist\r\n- 🔄 Switching to Chinese should redirect to `/zh/blogs` with a notification\r\n\r\nThis helps ensure our intelligent language switching system works correctly in all scenarios.",
    "_meta": {
      "filePath": "en/english-only-test.mdx",
      "fileName": "english-only-test.mdx",
      "directory": "en",
      "extension": "mdx",
      "path": "en/english-only-test"
    },
    "lang": "en",
    "url": "/en/blogs/english-only-test",
    "canonicalUrl": "https://shipany.ai/en/blogs/english-only-test",
    "mdx": "var Component=(()=>{var g=Object.create;var r=Object.defineProperty;var u=Object.getOwnPropertyDescriptor;var p=Object.getOwnPropertyNames;var w=Object.getPrototypeOf,m=Object.prototype.hasOwnProperty;var x=(i,e)=>()=>(e||i((e={exports:{}}).exports,e),e.exports),v=(i,e)=>{for(var t in e)r(i,t,{get:e[t],enumerable:!0})},h=(i,e,t,s)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let l of p(e))!m.call(i,l)&&l!==t&&r(i,l,{get:()=>e[l],enumerable:!(s=u(e,l))||s.enumerable});return i};var y=(i,e,t)=>(t=i!=null?g(w(i)):{},h(e||!i||!i.__esModule?r(t,\"default\",{value:i,enumerable:!0}):t,i)),f=i=>h(r({},\"__esModule\",{value:!0}),i);var o=x((_,c)=>{c.exports=_jsx_runtime});var b={};v(b,{default:()=>d});var n=y(o());function a(i){let e={code:\"code\",h1:\"h1\",h2:\"h2\",li:\"li\",ol:\"ol\",p:\"p\",strong:\"strong\",ul:\"ul\",...i.components};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(e.h1,{children:\"English Only Test Article\"}),`\n`,(0,n.jsx)(e.p,{children:\"This article is specifically created to test the language switching functionality when content is not available in all languages.\"}),`\n`,(0,n.jsx)(e.h2,{children:\"Purpose\"}),`\n`,(0,n.jsx)(e.p,{children:\"When a user tries to switch to Chinese (\\u4E2D\\u6587) while viewing this article, they should be redirected to the Chinese blog list page since this article doesn't have a Chinese version.\"}),`\n`,(0,n.jsx)(e.h2,{children:\"Testing Scenarios\"}),`\n`,(0,n.jsxs)(e.ol,{children:[`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"Direct Language Switch\"}),\": Use the header language selector\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"Language Versions Indicator\"}),\": Use the language versions component on this page\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"Fallback Behavior\"}),\": Verify that users are redirected appropriately\"]}),`\n`]}),`\n`,(0,n.jsx)(e.h2,{children:\"Expected Behavior\"}),`\n`,(0,n.jsxs)(e.ul,{children:[`\n`,(0,n.jsx)(e.li,{children:\"\\u2705 English version should be accessible\"}),`\n`,(0,n.jsx)(e.li,{children:\"\\u274C Chinese version should not exist\"}),`\n`,(0,n.jsxs)(e.li,{children:[\"\\u{1F504} Switching to Chinese should redirect to \",(0,n.jsx)(e.code,{children:\"/zh/blogs\"}),\" with a notification\"]}),`\n`]}),`\n`,(0,n.jsx)(e.p,{children:\"This helps ensure our intelligent language switching system works correctly in all scenarios.\"})]})}function d(i={}){let{wrapper:e}=i.components||{};return e?(0,n.jsx)(e,{...i,children:(0,n.jsx)(a,{...i})}):a(i)}return f(b);})();\n;return Component;",
    "readingTime": 1,
    "wordCount": 132,
    "lastModified": new Date("2025-07-20T16:59:26.753Z"),
    "contentType": "blogs",
    "_cached": true,
    "_version": "2.0"
  },
  {
    "title": "Getting Started with ShipAny: Build Your AI SaaS in Hours",
    "slug": "getting-started-with-shipany",
    "description": "Learn how to quickly build and deploy your AI SaaS application using ShipAny's powerful template and components.",
    "coverImage": "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop",
    "author": "ShipAny Team",
    "authorImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    "publishedAt": new Date("2025-01-17T00:00:00.000Z"),
    "featured": true,
    "tags": [
      "tutorial",
      "getting-started",
      "ai-saas"
    ],
    "keywords": [],
    "content": "# Getting Started with ShipAny\r\n\r\nWelcome to ShipAny! This comprehensive guide will walk you through building your first AI SaaS application using our powerful template system.\r\n\r\n## What is ShipAny?\r\n\r\nShipAny is a Next.js boilerplate designed specifically for building AI SaaS startups quickly and efficiently. With pre-built components, authentication, payment processing, and AI integrations, you can focus on your unique value proposition rather than boilerplate code.\r\n\r\n## Key Features\r\n\r\n- **🚀 Rapid Development**: Pre-built components and templates\r\n- **🤖 AI Integration**: Ready-to-use AI SDK integrations\r\n- **💳 Payment Processing**: Stripe integration out of the box\r\n- **🌍 Internationalization**: Multi-language support\r\n- **📱 Responsive Design**: Mobile-first approach\r\n- **🔐 Authentication**: Secure user management\r\n\r\n## Quick Start\r\n\r\n### 1. Clone the Repository\r\n\r\n```bash\r\ngit clone https://github.com/shipany/shipany-template\r\ncd shipany-template\r\n```\r\n\r\n### 2. Install Dependencies\r\n\r\n```bash\r\npnpm install\r\n```\r\n\r\n### 3. Set Up Environment Variables\r\n\r\nCreate a `.env.local` file with your configuration:\r\n\r\n```env\r\nNEXT_PUBLIC_WEB_URL=http://localhost:3000\r\nDATABASE_URL=your_database_url\r\nNEXTAUTH_SECRET=your_secret\r\nSTRIPE_SECRET_KEY=your_stripe_key\r\n```\r\n\r\n### 4. Run the Development Server\r\n\r\n```bash\r\npnpm dev\r\n```\r\n\r\n## Building Your First Feature\r\n\r\nLet's create a simple AI-powered text generator:\r\n\r\n### 1. Create the API Route\r\n\r\n```typescript\r\n// app/api/generate/route.ts\r\nimport { openai } from '@ai-sdk/openai'\r\nimport { generateText } from 'ai'\r\n\r\nexport async function POST(request: Request) {\r\n  const { prompt } = await request.json()\r\n  \r\n  const { text } = await generateText({\r\n    model: openai('gpt-3.5-turbo'),\r\n    prompt: `Generate creative content based on: ${prompt}`,\r\n  })\r\n  \r\n  return Response.json({ text })\r\n}\r\n```\r\n\r\n### 2. Create the Frontend Component\r\n\r\n```tsx\r\n'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Textarea } from '@/components/ui/textarea'\r\n\r\nexport function TextGenerator() {\r\n  const [prompt, setPrompt] = useState('')\r\n  const [result, setResult] = useState('')\r\n  const [loading, setLoading] = useState(false)\r\n\r\n  const handleGenerate = async () => {\r\n    setLoading(true)\r\n    try {\r\n      const response = await fetch('/api/generate', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ prompt }),\r\n      })\r\n      const data = await response.json()\r\n      setResult(data.text)\r\n    } catch (error) {\r\n      console.error('Error:', error)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <Textarea\r\n        placeholder=\"Enter your prompt...\"\r\n        value={prompt}\r\n        onChange={(e) => setPrompt(e.target.value)}\r\n      />\r\n      <Button onClick={handleGenerate} disabled={loading}>\r\n        {loading ? 'Generating...' : 'Generate'}\r\n      </Button>\r\n      {result && (\r\n        <div className=\"p-4 bg-muted rounded-lg\">\r\n          {result}\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n```\r\n\r\n## Next Steps\r\n\r\nNow that you have the basics set up, you can:\r\n\r\n1. **Customize the UI**: Modify components to match your brand\r\n2. **Add More AI Features**: Integrate additional AI models\r\n3. **Set Up Payments**: Configure Stripe for subscriptions\r\n4. **Deploy**: Deploy to Vercel or your preferred platform\r\n\r\n## Conclusion\r\n\r\nShipAny provides everything you need to build and launch your AI SaaS quickly. With its comprehensive feature set and developer-friendly architecture, you can focus on what matters most: building great products for your users.\r\n\r\nReady to ship your next AI SaaS? Get started with ShipAny today!",
    "_meta": {
      "filePath": "en/getting-started-with-shipany.mdx",
      "fileName": "getting-started-with-shipany.mdx",
      "directory": "en",
      "extension": "mdx",
      "path": "en/getting-started-with-shipany"
    },
    "lang": "en",
    "url": "/en/blogs/getting-started-with-shipany",
    "canonicalUrl": "https://shipany.ai/en/blogs/getting-started-with-shipany",
    "mdx": "var Component=(()=>{var p=Object.create;var i=Object.defineProperty;var u=Object.getOwnPropertyDescriptor;var g=Object.getOwnPropertyNames;var m=Object.getPrototypeOf,y=Object.prototype.hasOwnProperty;var f=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),S=(r,e)=>{for(var t in e)i(r,t,{get:e[t],enumerable:!0})},l=(r,e,t,a)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let o of g(e))!y.call(r,o)&&o!==t&&i(r,o,{get:()=>e[o],enumerable:!(a=u(e,o))||a.enumerable});return r};var b=(r,e,t)=>(t=r!=null?p(m(r)):{},l(e||!r||!r.__esModule?i(t,\"default\",{value:r,enumerable:!0}):t,r)),x=r=>l(i({},\"__esModule\",{value:!0}),r);var c=f((_,s)=>{s.exports=_jsx_runtime});var A={};S(A,{default:()=>h});var n=b(c());function d(r){let e={code:\"code\",h1:\"h1\",h2:\"h2\",h3:\"h3\",li:\"li\",ol:\"ol\",p:\"p\",pre:\"pre\",strong:\"strong\",ul:\"ul\",...r.components};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(e.h1,{children:\"Getting Started with ShipAny\"}),`\n`,(0,n.jsx)(e.p,{children:\"Welcome to ShipAny! This comprehensive guide will walk you through building your first AI SaaS application using our powerful template system.\"}),`\n`,(0,n.jsx)(e.h2,{children:\"What is ShipAny?\"}),`\n`,(0,n.jsx)(e.p,{children:\"ShipAny is a Next.js boilerplate designed specifically for building AI SaaS startups quickly and efficiently. With pre-built components, authentication, payment processing, and AI integrations, you can focus on your unique value proposition rather than boilerplate code.\"}),`\n`,(0,n.jsx)(e.h2,{children:\"Key Features\"}),`\n`,(0,n.jsxs)(e.ul,{children:[`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"\\u{1F680} Rapid Development\"}),\": Pre-built components and templates\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"\\u{1F916} AI Integration\"}),\": Ready-to-use AI SDK integrations\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"\\u{1F4B3} Payment Processing\"}),\": Stripe integration out of the box\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"\\u{1F30D} Internationalization\"}),\": Multi-language support\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"\\u{1F4F1} Responsive Design\"}),\": Mobile-first approach\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"\\u{1F510} Authentication\"}),\": Secure user management\"]}),`\n`]}),`\n`,(0,n.jsx)(e.h2,{children:\"Quick Start\"}),`\n`,(0,n.jsx)(e.h3,{children:\"1. Clone the Repository\"}),`\n`,(0,n.jsx)(e.pre,{children:(0,n.jsx)(e.code,{className:\"language-bash\",children:`git clone https://github.com/shipany/shipany-template\\r\ncd shipany-template\n`})}),`\n`,(0,n.jsx)(e.h3,{children:\"2. Install Dependencies\"}),`\n`,(0,n.jsx)(e.pre,{children:(0,n.jsx)(e.code,{className:\"language-bash\",children:`pnpm install\n`})}),`\n`,(0,n.jsx)(e.h3,{children:\"3. Set Up Environment Variables\"}),`\n`,(0,n.jsxs)(e.p,{children:[\"Create a \",(0,n.jsx)(e.code,{children:\".env.local\"}),\" file with your configuration:\"]}),`\n`,(0,n.jsx)(e.pre,{children:(0,n.jsx)(e.code,{className:\"language-env\",children:`NEXT_PUBLIC_WEB_URL=http://localhost:3000\\r\nDATABASE_URL=your_database_url\\r\nNEXTAUTH_SECRET=your_secret\\r\nSTRIPE_SECRET_KEY=your_stripe_key\n`})}),`\n`,(0,n.jsx)(e.h3,{children:\"4. Run the Development Server\"}),`\n`,(0,n.jsx)(e.pre,{children:(0,n.jsx)(e.code,{className:\"language-bash\",children:`pnpm dev\n`})}),`\n`,(0,n.jsx)(e.h2,{children:\"Building Your First Feature\"}),`\n`,(0,n.jsx)(e.p,{children:\"Let's create a simple AI-powered text generator:\"}),`\n`,(0,n.jsx)(e.h3,{children:\"1. Create the API Route\"}),`\n`,(0,n.jsx)(e.pre,{children:(0,n.jsx)(e.code,{className:\"language-typescript\",children:`// app/api/generate/route.ts\\r\nimport { openai } from '@ai-sdk/openai'\\r\nimport { generateText } from 'ai'\\r\n\\r\nexport async function POST(request: Request) {\\r\n  const { prompt } = await request.json()\\r\n  \\r\n  const { text } = await generateText({\\r\n    model: openai('gpt-3.5-turbo'),\\r\n    prompt: \\`Generate creative content based on: \\${prompt}\\`,\\r\n  })\\r\n  \\r\n  return Response.json({ text })\\r\n}\n`})}),`\n`,(0,n.jsx)(e.h3,{children:\"2. Create the Frontend Component\"}),`\n`,(0,n.jsx)(e.pre,{children:(0,n.jsx)(e.code,{className:\"language-tsx\",children:`'use client'\\r\n\\r\nimport { useState } from 'react'\\r\nimport { Button } from '@/components/ui/button'\\r\nimport { Textarea } from '@/components/ui/textarea'\\r\n\\r\nexport function TextGenerator() {\\r\n  const [prompt, setPrompt] = useState('')\\r\n  const [result, setResult] = useState('')\\r\n  const [loading, setLoading] = useState(false)\\r\n\\r\n  const handleGenerate = async () => {\\r\n    setLoading(true)\\r\n    try {\\r\n      const response = await fetch('/api/generate', {\\r\n        method: 'POST',\\r\n        headers: { 'Content-Type': 'application/json' },\\r\n        body: JSON.stringify({ prompt }),\\r\n      })\\r\n      const data = await response.json()\\r\n      setResult(data.text)\\r\n    } catch (error) {\\r\n      console.error('Error:', error)\\r\n    } finally {\\r\n      setLoading(false)\\r\n    }\\r\n  }\\r\n\\r\n  return (\\r\n    <div className=\"space-y-4\">\\r\n      <Textarea\\r\n        placeholder=\"Enter your prompt...\"\\r\n        value={prompt}\\r\n        onChange={(e) => setPrompt(e.target.value)}\\r\n      />\\r\n      <Button onClick={handleGenerate} disabled={loading}>\\r\n        {loading ? 'Generating...' : 'Generate'}\\r\n      </Button>\\r\n      {result && (\\r\n        <div className=\"p-4 bg-muted rounded-lg\">\\r\n          {result}\\r\n        </div>\\r\n      )}\\r\n    </div>\\r\n  )\\r\n}\n`})}),`\n`,(0,n.jsx)(e.h2,{children:\"Next Steps\"}),`\n`,(0,n.jsx)(e.p,{children:\"Now that you have the basics set up, you can:\"}),`\n`,(0,n.jsxs)(e.ol,{children:[`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"Customize the UI\"}),\": Modify components to match your brand\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"Add More AI Features\"}),\": Integrate additional AI models\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"Set Up Payments\"}),\": Configure Stripe for subscriptions\"]}),`\n`,(0,n.jsxs)(e.li,{children:[(0,n.jsx)(e.strong,{children:\"Deploy\"}),\": Deploy to Vercel or your preferred platform\"]}),`\n`]}),`\n`,(0,n.jsx)(e.h2,{children:\"Conclusion\"}),`\n`,(0,n.jsx)(e.p,{children:\"ShipAny provides everything you need to build and launch your AI SaaS quickly. With its comprehensive feature set and developer-friendly architecture, you can focus on what matters most: building great products for your users.\"}),`\n`,(0,n.jsx)(e.p,{children:\"Ready to ship your next AI SaaS? Get started with ShipAny today!\"})]})}function h(r={}){let{wrapper:e}=r.components||{};return e?(0,n.jsx)(e,{...r,children:(0,n.jsx)(d,{...r})}):d(r)}return x(A);})();\n;return Component;",
    "readingTime": 3,
    "wordCount": 458,
    "lastModified": new Date("2025-07-20T16:59:26.754Z"),
    "contentType": "blogs",
    "_cached": true,
    "_version": "2.0"
  },
  {
    "title": "ShipAny 快速入门：几小时内构建你的 AI SaaS",
    "slug": "getting-started-with-shipany",
    "description": "学习如何使用 ShipAny 强大的模板和组件快速构建和部署你的 AI SaaS 应用程序。",
    "coverImage": "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop",
    "author": "ShipAny 团队",
    "authorImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    "publishedAt": new Date("2025-01-17T00:00:00.000Z"),
    "featured": true,
    "tags": [
      "教程",
      "快速入门",
      "ai-saas"
    ],
    "keywords": [],
    "content": "# ShipAny 快速入门\r\n\r\n欢迎使用 ShipAny！这份综合指南将带你使用我们强大的模板系统构建你的第一个 AI SaaS 应用程序。\r\n\r\n## 什么是 ShipAny？\r\n\r\nShipAny 是一个专为快速高效构建 AI SaaS 创业项目而设计的 Next.js 样板。通过预构建的组件、身份验证、支付处理和 AI 集成，你可以专注于你的独特价值主张，而不是样板代码。\r\n\r\n## 核心特性\r\n\r\n- **🚀 快速开发**：预构建的组件和模板\r\n- **🤖 AI 集成**：开箱即用的 AI SDK 集成\r\n- **💳 支付处理**：内置 Stripe 集成\r\n- **🌍 国际化**：多语言支持\r\n- **📱 响应式设计**：移动优先的方法\r\n- **🔐 身份验证**：安全的用户管理\r\n\r\n## 快速开始\r\n\r\n### 1. 克隆仓库\r\n\r\n```bash\r\ngit clone https://github.com/shipany/shipany-template\r\ncd shipany-template\r\n```\r\n\r\n### 2. 安装依赖\r\n\r\n```bash\r\npnpm install\r\n```\r\n\r\n### 3. 设置环境变量\r\n\r\n创建一个 `.env.local` 文件并配置：\r\n\r\n```env\r\nNEXT_PUBLIC_WEB_URL=http://localhost:3000\r\nDATABASE_URL=your_database_url\r\nNEXTAUTH_SECRET=your_secret\r\nSTRIPE_SECRET_KEY=your_stripe_key\r\n```\r\n\r\n### 4. 运行开发服务器\r\n\r\n```bash\r\npnpm dev\r\n```\r\n\r\n## 构建你的第一个功能\r\n\r\n让我们创建一个简单的 AI 驱动的文本生成器：\r\n\r\n### 1. 创建 API 路由\r\n\r\n```typescript\r\n// app/api/generate/route.ts\r\nimport { openai } from '@ai-sdk/openai'\r\nimport { generateText } from 'ai'\r\n\r\nexport async function POST(request: Request) {\r\n  const { prompt } = await request.json()\r\n  \r\n  const { text } = await generateText({\r\n    model: openai('gpt-3.5-turbo'),\r\n    prompt: `基于以下内容生成创意文本：${prompt}`,\r\n  })\r\n  \r\n  return Response.json({ text })\r\n}\r\n```\r\n\r\n### 2. 创建前端组件\r\n\r\n```tsx\r\n'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Textarea } from '@/components/ui/textarea'\r\n\r\nexport function TextGenerator() {\r\n  const [prompt, setPrompt] = useState('')\r\n  const [result, setResult] = useState('')\r\n  const [loading, setLoading] = useState(false)\r\n\r\n  const handleGenerate = async () => {\r\n    setLoading(true)\r\n    try {\r\n      const response = await fetch('/api/generate', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ prompt }),\r\n      })\r\n      const data = await response.json()\r\n      setResult(data.text)\r\n    } catch (error) {\r\n      console.error('错误:', error)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <Textarea\r\n        placeholder=\"输入你的提示...\"\r\n        value={prompt}\r\n        onChange={(e) => setPrompt(e.target.value)}\r\n      />\r\n      <Button onClick={handleGenerate} disabled={loading}>\r\n        {loading ? '生成中...' : '生成'}\r\n      </Button>\r\n      {result && (\r\n        <div className=\"p-4 bg-muted rounded-lg\">\r\n          {result}\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n```\r\n\r\n## 下一步\r\n\r\n现在你已经设置好了基础，你可以：\r\n\r\n1. **自定义 UI**：修改组件以匹配你的品牌\r\n2. **添加更多 AI 功能**：集成额外的 AI 模型\r\n3. **设置支付**：为订阅配置 Stripe\r\n4. **部署**：部署到 Vercel 或你首选的平台\r\n\r\n## 结论\r\n\r\nShipAny 提供了快速构建和启动 AI SaaS 所需的一切。凭借其全面的功能集和开发者友好的架构，你可以专注于最重要的事情：为用户构建优秀的产品。\r\n\r\n准备好发布你的下一个 AI SaaS 了吗？今天就开始使用 ShipAny 吧！",
    "_meta": {
      "filePath": "zh/shipany-kuai-su-ru-men.mdx",
      "fileName": "shipany-kuai-su-ru-men.mdx",
      "directory": "zh",
      "extension": "mdx",
      "path": "zh/shipany-kuai-su-ru-men"
    },
    "lang": "zh",
    "url": "/zh/blogs/getting-started-with-shipany",
    "canonicalUrl": "https://shipany.ai/zh/blogs/getting-started-with-shipany",
    "mdx": "var Component=(()=>{var p=Object.create;var l=Object.defineProperty;var u=Object.getOwnPropertyDescriptor;var g=Object.getOwnPropertyNames;var m=Object.getPrototypeOf,S=Object.prototype.hasOwnProperty;var y=(r,n)=>()=>(n||r((n={exports:{}}).exports,n),n.exports),x=(r,n)=>{for(var t in n)l(r,t,{get:n[t],enumerable:!0})},o=(r,n,t,a)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let i of g(n))!S.call(r,i)&&i!==t&&l(r,i,{get:()=>n[i],enumerable:!(a=u(n,i))||a.enumerable});return r};var A=(r,n,t)=>(t=r!=null?p(m(r)):{},o(n||!r||!r.__esModule?l(t,\"default\",{value:r,enumerable:!0}):t,r)),_=r=>o(l({},\"__esModule\",{value:!0}),r);var s=y((I,c)=>{c.exports=_jsx_runtime});var f={};x(f,{default:()=>d});var e=A(s());function h(r){let n={code:\"code\",h1:\"h1\",h2:\"h2\",h3:\"h3\",li:\"li\",ol:\"ol\",p:\"p\",pre:\"pre\",strong:\"strong\",ul:\"ul\",...r.components};return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(n.h1,{children:\"ShipAny \\u5FEB\\u901F\\u5165\\u95E8\"}),`\n`,(0,e.jsx)(n.p,{children:\"\\u6B22\\u8FCE\\u4F7F\\u7528 ShipAny\\uFF01\\u8FD9\\u4EFD\\u7EFC\\u5408\\u6307\\u5357\\u5C06\\u5E26\\u4F60\\u4F7F\\u7528\\u6211\\u4EEC\\u5F3A\\u5927\\u7684\\u6A21\\u677F\\u7CFB\\u7EDF\\u6784\\u5EFA\\u4F60\\u7684\\u7B2C\\u4E00\\u4E2A AI SaaS \\u5E94\\u7528\\u7A0B\\u5E8F\\u3002\"}),`\n`,(0,e.jsx)(n.h2,{children:\"\\u4EC0\\u4E48\\u662F ShipAny\\uFF1F\"}),`\n`,(0,e.jsx)(n.p,{children:\"ShipAny \\u662F\\u4E00\\u4E2A\\u4E13\\u4E3A\\u5FEB\\u901F\\u9AD8\\u6548\\u6784\\u5EFA AI SaaS \\u521B\\u4E1A\\u9879\\u76EE\\u800C\\u8BBE\\u8BA1\\u7684 Next.js \\u6837\\u677F\\u3002\\u901A\\u8FC7\\u9884\\u6784\\u5EFA\\u7684\\u7EC4\\u4EF6\\u3001\\u8EAB\\u4EFD\\u9A8C\\u8BC1\\u3001\\u652F\\u4ED8\\u5904\\u7406\\u548C AI \\u96C6\\u6210\\uFF0C\\u4F60\\u53EF\\u4EE5\\u4E13\\u6CE8\\u4E8E\\u4F60\\u7684\\u72EC\\u7279\\u4EF7\\u503C\\u4E3B\\u5F20\\uFF0C\\u800C\\u4E0D\\u662F\\u6837\\u677F\\u4EE3\\u7801\\u3002\"}),`\n`,(0,e.jsx)(n.h2,{children:\"\\u6838\\u5FC3\\u7279\\u6027\"}),`\n`,(0,e.jsxs)(n.ul,{children:[`\n`,(0,e.jsxs)(n.li,{children:[(0,e.jsx)(n.strong,{children:\"\\u{1F680} \\u5FEB\\u901F\\u5F00\\u53D1\"}),\"\\uFF1A\\u9884\\u6784\\u5EFA\\u7684\\u7EC4\\u4EF6\\u548C\\u6A21\\u677F\"]}),`\n`,(0,e.jsxs)(n.li,{children:[(0,e.jsx)(n.strong,{children:\"\\u{1F916} AI \\u96C6\\u6210\"}),\"\\uFF1A\\u5F00\\u7BB1\\u5373\\u7528\\u7684 AI SDK \\u96C6\\u6210\"]}),`\n`,(0,e.jsxs)(n.li,{children:[(0,e.jsx)(n.strong,{children:\"\\u{1F4B3} \\u652F\\u4ED8\\u5904\\u7406\"}),\"\\uFF1A\\u5185\\u7F6E Stripe \\u96C6\\u6210\"]}),`\n`,(0,e.jsxs)(n.li,{children:[(0,e.jsx)(n.strong,{children:\"\\u{1F30D} \\u56FD\\u9645\\u5316\"}),\"\\uFF1A\\u591A\\u8BED\\u8A00\\u652F\\u6301\"]}),`\n`,(0,e.jsxs)(n.li,{children:[(0,e.jsx)(n.strong,{children:\"\\u{1F4F1} \\u54CD\\u5E94\\u5F0F\\u8BBE\\u8BA1\"}),\"\\uFF1A\\u79FB\\u52A8\\u4F18\\u5148\\u7684\\u65B9\\u6CD5\"]}),`\n`,(0,e.jsxs)(n.li,{children:[(0,e.jsx)(n.strong,{children:\"\\u{1F510} \\u8EAB\\u4EFD\\u9A8C\\u8BC1\"}),\"\\uFF1A\\u5B89\\u5168\\u7684\\u7528\\u6237\\u7BA1\\u7406\"]}),`\n`]}),`\n`,(0,e.jsx)(n.h2,{children:\"\\u5FEB\\u901F\\u5F00\\u59CB\"}),`\n`,(0,e.jsx)(n.h3,{children:\"1. \\u514B\\u9686\\u4ED3\\u5E93\"}),`\n`,(0,e.jsx)(n.pre,{children:(0,e.jsx)(n.code,{className:\"language-bash\",children:`git clone https://github.com/shipany/shipany-template\\r\ncd shipany-template\n`})}),`\n`,(0,e.jsx)(n.h3,{children:\"2. \\u5B89\\u88C5\\u4F9D\\u8D56\"}),`\n`,(0,e.jsx)(n.pre,{children:(0,e.jsx)(n.code,{className:\"language-bash\",children:`pnpm install\n`})}),`\n`,(0,e.jsx)(n.h3,{children:\"3. \\u8BBE\\u7F6E\\u73AF\\u5883\\u53D8\\u91CF\"}),`\n`,(0,e.jsxs)(n.p,{children:[\"\\u521B\\u5EFA\\u4E00\\u4E2A \",(0,e.jsx)(n.code,{children:\".env.local\"}),\" \\u6587\\u4EF6\\u5E76\\u914D\\u7F6E\\uFF1A\"]}),`\n`,(0,e.jsx)(n.pre,{children:(0,e.jsx)(n.code,{className:\"language-env\",children:`NEXT_PUBLIC_WEB_URL=http://localhost:3000\\r\nDATABASE_URL=your_database_url\\r\nNEXTAUTH_SECRET=your_secret\\r\nSTRIPE_SECRET_KEY=your_stripe_key\n`})}),`\n`,(0,e.jsx)(n.h3,{children:\"4. \\u8FD0\\u884C\\u5F00\\u53D1\\u670D\\u52A1\\u5668\"}),`\n`,(0,e.jsx)(n.pre,{children:(0,e.jsx)(n.code,{className:\"language-bash\",children:`pnpm dev\n`})}),`\n`,(0,e.jsx)(n.h2,{children:\"\\u6784\\u5EFA\\u4F60\\u7684\\u7B2C\\u4E00\\u4E2A\\u529F\\u80FD\"}),`\n`,(0,e.jsx)(n.p,{children:\"\\u8BA9\\u6211\\u4EEC\\u521B\\u5EFA\\u4E00\\u4E2A\\u7B80\\u5355\\u7684 AI \\u9A71\\u52A8\\u7684\\u6587\\u672C\\u751F\\u6210\\u5668\\uFF1A\"}),`\n`,(0,e.jsx)(n.h3,{children:\"1. \\u521B\\u5EFA API \\u8DEF\\u7531\"}),`\n`,(0,e.jsx)(n.pre,{children:(0,e.jsx)(n.code,{className:\"language-typescript\",children:`// app/api/generate/route.ts\\r\nimport { openai } from '@ai-sdk/openai'\\r\nimport { generateText } from 'ai'\\r\n\\r\nexport async function POST(request: Request) {\\r\n  const { prompt } = await request.json()\\r\n  \\r\n  const { text } = await generateText({\\r\n    model: openai('gpt-3.5-turbo'),\\r\n    prompt: \\`\\u57FA\\u4E8E\\u4EE5\\u4E0B\\u5185\\u5BB9\\u751F\\u6210\\u521B\\u610F\\u6587\\u672C\\uFF1A\\${prompt}\\`,\\r\n  })\\r\n  \\r\n  return Response.json({ text })\\r\n}\n`})}),`\n`,(0,e.jsx)(n.h3,{children:\"2. \\u521B\\u5EFA\\u524D\\u7AEF\\u7EC4\\u4EF6\"}),`\n`,(0,e.jsx)(n.pre,{children:(0,e.jsx)(n.code,{className:\"language-tsx\",children:`'use client'\\r\n\\r\nimport { useState } from 'react'\\r\nimport { Button } from '@/components/ui/button'\\r\nimport { Textarea } from '@/components/ui/textarea'\\r\n\\r\nexport function TextGenerator() {\\r\n  const [prompt, setPrompt] = useState('')\\r\n  const [result, setResult] = useState('')\\r\n  const [loading, setLoading] = useState(false)\\r\n\\r\n  const handleGenerate = async () => {\\r\n    setLoading(true)\\r\n    try {\\r\n      const response = await fetch('/api/generate', {\\r\n        method: 'POST',\\r\n        headers: { 'Content-Type': 'application/json' },\\r\n        body: JSON.stringify({ prompt }),\\r\n      })\\r\n      const data = await response.json()\\r\n      setResult(data.text)\\r\n    } catch (error) {\\r\n      console.error('\\u9519\\u8BEF:', error)\\r\n    } finally {\\r\n      setLoading(false)\\r\n    }\\r\n  }\\r\n\\r\n  return (\\r\n    <div className=\"space-y-4\">\\r\n      <Textarea\\r\n        placeholder=\"\\u8F93\\u5165\\u4F60\\u7684\\u63D0\\u793A...\"\\r\n        value={prompt}\\r\n        onChange={(e) => setPrompt(e.target.value)}\\r\n      />\\r\n      <Button onClick={handleGenerate} disabled={loading}>\\r\n        {loading ? '\\u751F\\u6210\\u4E2D...' : '\\u751F\\u6210'}\\r\n      </Button>\\r\n      {result && (\\r\n        <div className=\"p-4 bg-muted rounded-lg\">\\r\n          {result}\\r\n        </div>\\r\n      )}\\r\n    </div>\\r\n  )\\r\n}\n`})}),`\n`,(0,e.jsx)(n.h2,{children:\"\\u4E0B\\u4E00\\u6B65\"}),`\n`,(0,e.jsx)(n.p,{children:\"\\u73B0\\u5728\\u4F60\\u5DF2\\u7ECF\\u8BBE\\u7F6E\\u597D\\u4E86\\u57FA\\u7840\\uFF0C\\u4F60\\u53EF\\u4EE5\\uFF1A\"}),`\n`,(0,e.jsxs)(n.ol,{children:[`\n`,(0,e.jsxs)(n.li,{children:[(0,e.jsx)(n.strong,{children:\"\\u81EA\\u5B9A\\u4E49 UI\"}),\"\\uFF1A\\u4FEE\\u6539\\u7EC4\\u4EF6\\u4EE5\\u5339\\u914D\\u4F60\\u7684\\u54C1\\u724C\"]}),`\n`,(0,e.jsxs)(n.li,{children:[(0,e.jsx)(n.strong,{children:\"\\u6DFB\\u52A0\\u66F4\\u591A AI \\u529F\\u80FD\"}),\"\\uFF1A\\u96C6\\u6210\\u989D\\u5916\\u7684 AI \\u6A21\\u578B\"]}),`\n`,(0,e.jsxs)(n.li,{children:[(0,e.jsx)(n.strong,{children:\"\\u8BBE\\u7F6E\\u652F\\u4ED8\"}),\"\\uFF1A\\u4E3A\\u8BA2\\u9605\\u914D\\u7F6E Stripe\"]}),`\n`,(0,e.jsxs)(n.li,{children:[(0,e.jsx)(n.strong,{children:\"\\u90E8\\u7F72\"}),\"\\uFF1A\\u90E8\\u7F72\\u5230 Vercel \\u6216\\u4F60\\u9996\\u9009\\u7684\\u5E73\\u53F0\"]}),`\n`]}),`\n`,(0,e.jsx)(n.h2,{children:\"\\u7ED3\\u8BBA\"}),`\n`,(0,e.jsx)(n.p,{children:\"ShipAny \\u63D0\\u4F9B\\u4E86\\u5FEB\\u901F\\u6784\\u5EFA\\u548C\\u542F\\u52A8 AI SaaS \\u6240\\u9700\\u7684\\u4E00\\u5207\\u3002\\u51ED\\u501F\\u5176\\u5168\\u9762\\u7684\\u529F\\u80FD\\u96C6\\u548C\\u5F00\\u53D1\\u8005\\u53CB\\u597D\\u7684\\u67B6\\u6784\\uFF0C\\u4F60\\u53EF\\u4EE5\\u4E13\\u6CE8\\u4E8E\\u6700\\u91CD\\u8981\\u7684\\u4E8B\\u60C5\\uFF1A\\u4E3A\\u7528\\u6237\\u6784\\u5EFA\\u4F18\\u79C0\\u7684\\u4EA7\\u54C1\\u3002\"}),`\n`,(0,e.jsx)(n.p,{children:\"\\u51C6\\u5907\\u597D\\u53D1\\u5E03\\u4F60\\u7684\\u4E0B\\u4E00\\u4E2A AI SaaS \\u4E86\\u5417\\uFF1F\\u4ECA\\u5929\\u5C31\\u5F00\\u59CB\\u4F7F\\u7528 ShipAny \\u5427\\uFF01\"})]})}function d(r={}){let{wrapper:n}=r.components||{};return n?(0,e.jsx)(n,{...r,children:(0,e.jsx)(h,{...r})}):h(r)}return _(f);})();\n;return Component;",
    "readingTime": 2,
    "wordCount": 295,
    "lastModified": new Date("2025-07-20T16:59:26.754Z"),
    "contentType": "blogs",
    "_cached": true,
    "_version": "2.0"
  }
]