import { APIResource } from "../../../../resource.js";
import * as EventsAPI from "./events.js";
import { EventListParams, EventListResponse, EventListResponsesV4PagePagination, Events } from "./events.js";
export declare class Leaks extends APIResource {
    events: EventsAPI.Events;
}
export declare namespace Leaks {
    export { Events as Events, type EventListResponse as EventListResponse, EventListResponsesV4PagePagination as EventListResponsesV4PagePagination, type EventListParams as EventListParams, };
}
//# sourceMappingURL=leaks.d.ts.map