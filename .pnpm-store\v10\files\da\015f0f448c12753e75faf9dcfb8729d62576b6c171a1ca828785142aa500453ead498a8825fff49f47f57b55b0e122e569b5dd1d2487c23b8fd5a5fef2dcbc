{"version": 3, "file": "entries.mjs", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/dlp/entries/entries.ts"], "names": [], "mappings": "AAAA,sFAAsF;OAE/E,EAAE,WAAW,EAAE;OAEf,KAAK,SAAS;OACd,EACL,MAAM,GAOP;OACM,KAAK,cAAc;OACnB,EACL,WAAW,GAOZ;OACM,KAAK,aAAa;OAClB,EACL,UAAU,GAOX;OAEM,EAAE,UAAU,EAAE;AAErB,MAAM,OAAO,OAAQ,SAAQ,WAAW;IAAxC;;QACE,WAAM,GAAqB,IAAI,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9D,eAAU,GAA6B,IAAI,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAClF,gBAAW,GAA+B,IAAI,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IA+HzF,CAAC;IA7HC;;;;;;;;;;;;;OAaG;IACH,MAAM,CAAC,MAAyB,EAAE,OAA6B;QAC7D,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QACvC,OACE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,UAAU,cAAc,EAAE,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,CAG9E,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,MAAM,CACJ,OAAe,EACf,MAAyB,EACzB,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QACvC,OACE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,UAAU,gBAAgB,OAAO,EAAE,EAAE;YACjE,IAAI;YACJ,GAAG,OAAO;SACX,CACF,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,IAAI,CACF,MAAuB,EACvB,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAC5B,aAAa,UAAU,cAAc,EACrC,4BAA4B,EAC5B,OAAO,CACR,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACH,MAAM,CACJ,OAAe,EACf,MAAyB,EACzB,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QAC9B,OACE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,UAAU,gBAAgB,OAAO,EAAE,EAAE,OAAO,CAG9E,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;OAUG;IACH,GAAG,CACD,OAAe,EACf,MAAsB,EACtB,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QAC9B,OACE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,UAAU,gBAAgB,OAAO,EAAE,EAAE,OAAO,CAG3E,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;CACF;AAED,MAAM,OAAO,4BAA6B,SAAQ,UAA6B;CAAG;AA4flF,OAAO,CAAC,4BAA4B,GAAG,4BAA4B,CAAC;AACpE,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AACxB,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;AAChC,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC"}