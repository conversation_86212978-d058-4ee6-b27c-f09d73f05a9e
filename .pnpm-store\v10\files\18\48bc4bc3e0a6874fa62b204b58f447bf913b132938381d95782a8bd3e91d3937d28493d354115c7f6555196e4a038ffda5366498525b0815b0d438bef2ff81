// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import * as EventsAPI from "./events.mjs";
import { EventListResponsesV4PagePagination, Events } from "./events.mjs";
export class Leaks extends APIResource {
    constructor() {
        super(...arguments);
        this.events = new EventsAPI.Events(this._client);
    }
}
Leaks.Events = Events;
Leaks.EventListResponsesV4PagePagination = EventListResponsesV4PagePagination;
//# sourceMappingURL=leaks.mjs.map