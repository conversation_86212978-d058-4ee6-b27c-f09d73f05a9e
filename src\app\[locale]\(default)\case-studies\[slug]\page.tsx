import { allCaseStudies } from 'content-collections'
import { notFound } from 'next/navigation'
import { setRequestLocale } from 'next-intl/server'
import { Mdx } from '@/components/mdx'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import Image from 'next/image'
import { Metadata } from 'next'
import ContentLanguageIndicator from '@/components/locale/content-language-indicator'

export async function generateStaticParams() {
  return allCaseStudies.map(caseStudy => ({ 
    locale: caseStudy.lang, 
    slug: caseStudy.slug 
  }))
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>
}): Promise<Metadata> {
  const { locale, slug } = await params
  
  const caseStudy = allCaseStudies.find(cs => cs.lang === locale && cs.slug === slug)

  if (!caseStudy) {
    return {
      title: 'Case Study Not Found',
    }
  }

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/case-studies/${slug}`

  if (locale !== 'en') {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/case-studies/${slug}`
  }

  return {
    title: caseStudy.title,
    description: caseStudy.description,
    alternates: {
      canonical: canonicalUrl,
    },
  }
}

export default async function CaseStudyPage({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>
}) {
  const { locale, slug } = await params
  setRequestLocale(locale)

  const caseStudy = allCaseStudies.find(cs => cs.lang === locale && cs.slug === slug)

  if (!caseStudy) {
    notFound()
  }

  return (
    <section className="py-16">
      <div className="container">
        <div className="mx-auto max-w-4xl">
          {/* Header */}
          <div className="mb-8">
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                {caseStudy.featured && (
                  <Badge variant="secondary">Featured</Badge>
                )}
                {caseStudy.tags && caseStudy.tags.map((tag) => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
              <ContentLanguageIndicator variant="compact" />
            </div>
            
            <h1 className="mb-4 text-4xl font-bold tracking-tight">
              {caseStudy.title}
            </h1>
            
            {caseStudy.description && (
              <p className="text-xl text-muted-foreground">
                {caseStudy.description}
              </p>
            )}

            {(caseStudy.author || caseStudy.publishedAt) && (
              <div className="mt-6 flex items-center gap-4 text-sm text-muted-foreground">
                {caseStudy.author && (
                  <div className="flex items-center gap-2">
                    {caseStudy.authorImage && (
                      <Image
                        src={caseStudy.authorImage}
                        alt={caseStudy.author}
                        width={24}
                        height={24}
                        className="rounded-full"
                      />
                    )}
                    <span>By {caseStudy.author}</span>
                  </div>
                )}
                {caseStudy.publishedAt && (
                  <time dateTime={caseStudy.publishedAt}>
                    {new Date(caseStudy.publishedAt).toLocaleDateString(locale, {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </time>
                )}
              </div>
            )}
          </div>

          {/* Cover Image */}
          {caseStudy.coverImage && (
            <div className="mb-8">
              <Image
                src={caseStudy.coverImage}
                alt={caseStudy.title}
                width={800}
                height={400}
                className="w-full rounded-lg border object-cover"
              />
            </div>
          )}

          {/* Content */}
          <Card>
            <CardContent className="p-8">
              <Mdx code={caseStudy.mdx} />
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
