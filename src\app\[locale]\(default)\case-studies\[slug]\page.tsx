import { notFound } from 'next/navigation'
import { setRequestLocale } from 'next-intl/server'
import { Mdx } from '@/components/mdx'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Metadata } from 'next'
import ContentLanguageIndicator from '@/components/locale/content-language-indicator'
import { cms, initializeCMS } from '@/cms'
import {
  ContentCoverImage,
  AuthorImage,
  ContentStructuredData,
  ContentMetaTags
} from '@/components/content'
import type { CaseStudyContent } from '@/cms/types'

export async function generateStaticParams() {
  // Initialize CMS
  await initializeCMS()

  // Get all case studies from both languages
  const [enCaseStudies, zhCaseStudies] = await Promise.all([
    cms.getContentList<CaseStudyContent>('case-study', 'en'),
    cms.getContentList<CaseStudyContent>('case-study', 'zh')
  ])

  const allCaseStudies = [...enCaseStudies, ...zhCaseStudies]

  return allCaseStudies.map(caseStudy => ({
    locale: caseStudy.lang,
    slug: caseStudy.slug
  }))
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>
}): Promise<Metadata> {
  const { locale, slug } = await params
  
  // Initialize CMS
  await initializeCMS()

  const caseStudy = await cms.getContent<CaseStudyContent>('case-study', slug, locale)

  if (!caseStudy) {
    return {
      title: 'Case Study Not Found',
    }
  }

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/case-studies/${slug}`

  if (locale !== 'en') {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/case-studies/${slug}`
  }

  return {
    title: caseStudy.title,
    description: caseStudy.description,
    alternates: {
      canonical: canonicalUrl,
    },
  }
}

export default async function CaseStudyPage({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>
}) {
  const { locale, slug } = await params
  setRequestLocale(locale)

  // Initialize CMS
  await initializeCMS()

  const caseStudy = await cms.getContent<CaseStudyContent>('case-study', slug, locale)

  if (!caseStudy) {
    notFound()
  }

  // Get related case studies
  const relatedCaseStudies = await cms.getRelatedContent<CaseStudyContent>('case-study', slug, locale, 3)

  return (
    <>
      {/* SEO Components */}
      <ContentStructuredData content={caseStudy} />
      <ContentMetaTags content={caseStudy} />

      <section className="py-16">
        <div className="container">
          <div className="mx-auto max-w-4xl">
          {/* Header */}
          <div className="mb-8">
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                {caseStudy.featured && (
                  <Badge variant="secondary">Featured</Badge>
                )}
                {caseStudy.tags && caseStudy.tags.map((tag) => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
              <ContentLanguageIndicator variant="compact" />
            </div>
            
            <h1 className="mb-4 text-4xl font-bold tracking-tight">
              {caseStudy.title}
            </h1>
            
            {caseStudy.description && (
              <p className="text-xl text-muted-foreground">
                {caseStudy.description}
              </p>
            )}

            {(caseStudy.author || caseStudy.publishedAt) && (
              <div className="mt-6 flex items-center gap-4 text-sm text-muted-foreground">
                {caseStudy.author && (
                  <div className="flex items-center gap-2">
                    {caseStudy.authorImage && (
                      <AuthorImage
                        image={caseStudy.authorImage}
                        author={caseStudy.author}
                        size="sm"
                      />
                    )}
                    <span>By {caseStudy.author}</span>
                  </div>
                )}
                {caseStudy.publishedAt && (
                  <time dateTime={caseStudy.publishedAt}>
                    {new Date(caseStudy.publishedAt).toLocaleDateString(locale, {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </time>
                )}
              </div>
            )}
          </div>

          {/* Cover Image */}
          {caseStudy.coverImage && (
            <div className="mb-8">
              <ContentCoverImage
                image={caseStudy.coverImage}
                title={caseStudy.title}
                priority
              />
            </div>
          )}

          {/* Content */}
          <Card>
            <CardContent className="p-8">
              <Mdx code={caseStudy.content} />
            </CardContent>
          </Card>

          {/* Related Case Studies */}
          {relatedCaseStudies.length > 0 && (
            <div className="mt-12">
              <h2 className="mb-6 text-2xl font-bold">Related Case Studies</h2>
              <div className="grid gap-6 md:grid-cols-3">
                {relatedCaseStudies.map((relatedCase) => (
                  <Card key={relatedCase.slug}>
                    <CardContent className="p-4">
                      {relatedCase.coverImage && (
                        <div className="mb-3">
                          <ContentCoverImage
                            image={relatedCase.coverImage}
                            title={relatedCase.title}
                            className="w-full rounded object-cover h-32"
                          />
                        </div>
                      )}
                      <h3 className="font-semibold mb-2">{relatedCase.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {relatedCase.excerpt}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
    </>
  )
}
