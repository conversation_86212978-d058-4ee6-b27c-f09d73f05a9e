// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import * as NetworkPathAPI from "./network-path.mjs";
import { NetworkPath } from "./network-path.mjs";
export class TracerouteTestResults extends APIResource {
    constructor() {
        super(...arguments);
        this.networkPath = new NetworkPathAPI.NetworkPath(this._client);
    }
}
TracerouteTestResults.NetworkPath = NetworkPath;
//# sourceMappingURL=traceroute-test-results.mjs.map