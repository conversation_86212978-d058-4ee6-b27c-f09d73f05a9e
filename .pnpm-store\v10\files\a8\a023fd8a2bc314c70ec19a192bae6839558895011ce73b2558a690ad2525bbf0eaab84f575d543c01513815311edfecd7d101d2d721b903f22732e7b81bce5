{"version": 3, "file": "predefined.d.ts", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/dlp/profiles/predefined.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AACzC,OAAO,KAAK,SAAS,MAAM,UAAU,CAAC;AACtC,OAAO,KAAK,WAAW,MAAM,YAAY,CAAC;AAE1C,qBAAa,UAAW,SAAQ,WAAW;IACzC;;;;;;;;;;;OAWG;IACH,MAAM,CACJ,MAAM,EAAE,sBAAsB,EAC9B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC;IAUvC;;;;;;;;;;;OAWG;IACH,MAAM,CACJ,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,sBAAsB,EAC9B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC;IAUvC;;;;;;;;;;;;OAYG;IACH,MAAM,CACJ,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,sBAAsB,EAC9B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,wBAAwB,GAAG,IAAI,CAAC;IAUnD;;;;;;;;;;;OAWG;IACH,GAAG,CACD,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,mBAAmB,EAC3B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC;CASxC;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX,mBAAmB,EAAE,MAAM,CAAC;IAE5B,OAAO,EAAE,KAAK,CACV,iBAAiB,CAAC,WAAW,GAC7B,iBAAiB,CAAC,eAAe,GACjC,iBAAiB,CAAC,gBAAgB,GAClC,iBAAiB,CAAC,cAAc,GAChC,iBAAiB,CAAC,wBAAwB,GAC1C,iBAAiB,CAAC,aAAa,CAClC,CAAC;IAEF;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAE7B,oBAAoB,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,WAAW,CAAC;IAE/D;;;OAGG;IACH,iBAAiB,CAAC,EAAE,WAAW,CAAC,gBAAgB,CAAC;IAEjD,WAAW,CAAC,EAAE,OAAO,CAAC;IAEtB;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;CACvB;AAED,yBAAiB,iBAAiB,CAAC;IACjC,UAAiB,WAAW;QAC1B,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,OAAO,EAAE,SAAS,CAAC,OAAO,CAAC;QAE3B,IAAI,EAAE,QAAQ,CAAC;QAEf,UAAU,EAAE,MAAM,CAAC;QAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC5B;IAED,UAAiB,eAAe;QAC9B,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,eAAe,CAAC,UAAU,CAAC;QAEvC,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,YAAY,CAAC;QAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC5B;IAED,UAAiB,eAAe,CAAC;QAC/B,UAAiB,UAAU;YACzB;;eAEG;YACH,oBAAoB,EAAE,OAAO,CAAC;YAE9B;;;eAGG;YACH,SAAS,EAAE,OAAO,CAAC;SACpB;KACF;IAED,UAAiB,gBAAgB;QAC/B,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,aAAa,CAAC;QAEpB,UAAU,EAAE,MAAM,CAAC;QAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC5B;IAED,UAAiB,cAAc;QAC7B,EAAE,EAAE,MAAM,CAAC;QAEX;;;WAGG;QACH,cAAc,EAAE,OAAO,CAAC;QAExB,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,MAAM,EAAE,OAAO,CAAC;QAEhB,IAAI,EAAE,YAAY,CAAC;QAEnB,UAAU,EAAE,MAAM,CAAC;KACpB;IAED,UAAiB,wBAAwB;QACvC,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,sBAAsB,CAAC;QAE7B,UAAU,EAAE,MAAM,CAAC;KACpB;IAED,UAAiB,aAAa;QAC5B,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,WAAW,CAAC;QAElB,UAAU,EAAE,MAAM,CAAC;QAEnB,SAAS,EAAE,OAAO,CAAC;QAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC5B;CACF;AAED,MAAM,MAAM,wBAAwB,GAAG,OAAO,CAAC;AAE/C,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAE7B;;OAEG;IACH,mBAAmB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEpC;;OAEG;IACH,oBAAoB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAErC;;;OAGG;IACH,iBAAiB,CAAC,EAAE,WAAW,CAAC,qBAAqB,CAAC;IAEtD;;OAEG;IACH,OAAO,CAAC,EAAE,KAAK,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;IAE9C;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;CACvB;AAED,yBAAiB,sBAAsB,CAAC;IACtC,UAAiB,KAAK;QACpB,EAAE,EAAE,MAAM,CAAC;QAEX,OAAO,EAAE,OAAO,CAAC;KAClB;CACF;AAED,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAE7B;;OAEG;IACH,mBAAmB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEpC;;OAEG;IACH,oBAAoB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAErC;;;OAGG;IACH,iBAAiB,CAAC,EAAE,WAAW,CAAC,qBAAqB,CAAC;IAEtD;;OAEG;IACH,OAAO,CAAC,EAAE,KAAK,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;IAE9C;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;CACvB;AAED,yBAAiB,sBAAsB,CAAC;IACtC,UAAiB,KAAK;QACpB,EAAE,EAAE,MAAM,CAAC;QAEX,OAAO,EAAE,OAAO,CAAC;KAClB;CACF;AAED,MAAM,WAAW,sBAAsB;IACrC,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,mBAAmB;IAClC,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,CAAC,OAAO,WAAW,UAAU,CAAC;IAClC,OAAO,EACL,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,wBAAwB,IAAI,wBAAwB,EACzD,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,mBAAmB,IAAI,mBAAmB,GAChD,CAAC;CACH"}