/**
 * Velite Configuration
 * 
 * This configuration file sets up <PERSON><PERSON><PERSON> to process MDX files for our multi-language
 * content management system. <PERSON><PERSON><PERSON> transforms MDX files into type-safe TypeScript
 * objects with better performance and modern architecture compared to Contentlayer.
 * 
 * Key Features:
 * - Multi-language support (en/zh) with automatic language detection
 * - Unified schema for blogs, products, and case studies
 * - Automatic URL generation and metadata extraction
 * - Type-safe content access with full TypeScript support
 * - Enhanced image optimization and processing
 * - Better performance with incremental builds
 */

import { defineConfig, defineCollection, s } from 'velite'
import { blogSchema, productSchema, caseStudySchema } from './schemas'
import { cpus } from 'os'

export default defineConfig({
  // Content source directory
  root: './content',
  
  // Output configuration
  output: {
    data: '.velite',
    assets: 'public/static',
    base: '/static/',
    name: '[name]-[hash:8].[ext]',
    clean: true
  },
  
  // Performance optimizations
  cache: {
    enabled: true,
    dir: '.velite/cache'
  },
  
  // Parallel processing based on CPU cores
  concurrency: Math.max(1, Math.floor(cpus().length / 2)),
  
  // Development mode optimizations
  development: {
    // Skip heavy image processing in development for faster builds
    skipImageOptimization: process.env.NODE_ENV === 'development',
    // Reduce concurrency in development to avoid overwhelming the system
    concurrency: process.env.NODE_ENV === 'development' ? 2 : undefined
  },
  
  // Content collections definition
  collections: {
    // Blog posts collection
    blogs: defineCollection({
      name: 'Blog',
      pattern: 'blogs/**/*.mdx',
      schema: blogSchema
    }),
    
    // Products collection
    products: defineCollection({
      name: 'Product',
      pattern: 'products/**/*.mdx',
      schema: productSchema
    }),
    
    // Case studies collection
    caseStudies: defineCollection({
      name: 'CaseStudy',
      pattern: 'case-studies/**/*.mdx',
      schema: caseStudySchema
    })
  },
  
  // MDX processing configuration
  mdx: {
    // Enable development mode for faster processing
    development: process.env.NODE_ENV === 'development',
    
    // Remark plugins for markdown processing
    remarkPlugins: [
      // Add remark plugins here as needed
    ],
    
    // Rehype plugins for HTML processing
    rehypePlugins: [
      // Only enable heavy plugins in production
      ...(process.env.NODE_ENV === 'production' ? [
        // Add production-only rehype plugins here
      ] : [])
    ]
  },
  
  // Image processing configuration
  image: {
    // Enable image optimization in production
    enabled: process.env.NODE_ENV === 'production',
    formats: ['webp', 'jpeg'],
    sizes: [400, 800, 1200],
    quality: 80,
    // Generate blur placeholders in production only
    blur: process.env.NODE_ENV === 'production'
  },
  
  // Build hooks for performance monitoring
  prepare: async () => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 Starting Velite content processing...')
    }
  },
  
  complete: async () => {
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ Velite content processing completed')
    }
  }
})
