// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
export class CustomCertificate extends APIResource {
    /**
     * Fetches the current Zero Trust certificate configuration.
     *
     * @deprecated
     */
    get(params, options) {
        const { account_id } = params;
        return this._client.get(`/accounts/${account_id}/gateway/configuration/custom_certificate`, options);
    }
}
//# sourceMappingURL=custom-certificate.mjs.map