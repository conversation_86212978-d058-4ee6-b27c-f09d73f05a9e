[{"title": "ShipAny 快速入门：几小时内构建你的 AI SaaS", "slug": "getting-started-with-shipany", "description": "学习如何使用 ShipAny 强大的模板和组件快速构建和部署你的 AI SaaS 应用程序。", "coverImage": "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop", "author": "ShipAny 团队", "authorImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face", "publishedAt": "2025-01-17T00:00:00.000Z", "featured": true, "tags": ["教程", "快速入门", "ai-saas"], "lang": "en", "url": "/D:\\VSCodeWorkspace\\shipany-stwd\\content\\blogs\\zh\\shipany-kuai-su-ru-men.mdx/", "content": "const{Fragment:n,jsx:e,jsxs:r}=arguments[0];function _createMdxContent(t){const l={code:\"code\",h1:\"h1\",h2:\"h2\",h3:\"h3\",li:\"li\",ol:\"ol\",p:\"p\",pre:\"pre\",strong:\"strong\",ul:\"ul\",...t.components};return r(n,{children:[e(l.h1,{children:\"ShipAny 快速入门\"}),\"\\n\",e(l.p,{children:\"欢迎使用 ShipAny！这份综合指南将带你使用我们强大的模板系统构建你的第一个 AI SaaS 应用程序。\"}),\"\\n\",e(l.h2,{children:\"什么是 ShipAny？\"}),\"\\n\",e(l.p,{children:\"ShipAny 是一个专为快速高效构建 AI SaaS 创业项目而设计的 Next.js 样板。通过预构建的组件、身份验证、支付处理和 AI 集成，你可以专注于你的独特价值主张，而不是样板代码。\"}),\"\\n\",e(l.h2,{children:\"核心特性\"}),\"\\n\",r(l.ul,{children:[\"\\n\",r(l.li,{children:[e(l.strong,{children:\"🚀 快速开发\"}),\"：预构建的组件和模板\"]}),\"\\n\",r(l.li,{children:[e(l.strong,{children:\"🤖 AI 集成\"}),\"：开箱即用的 AI SDK 集成\"]}),\"\\n\",r(l.li,{children:[e(l.strong,{children:\"💳 支付处理\"}),\"：内置 Stripe 集成\"]}),\"\\n\",r(l.li,{children:[e(l.strong,{children:\"🌍 国际化\"}),\"：多语言支持\"]}),\"\\n\",r(l.li,{children:[e(l.strong,{children:\"📱 响应式设计\"}),\"：移动优先的方法\"]}),\"\\n\",r(l.li,{children:[e(l.strong,{children:\"🔐 身份验证\"}),\"：安全的用户管理\"]}),\"\\n\"]}),\"\\n\",e(l.h2,{children:\"快速开始\"}),\"\\n\",e(l.h3,{children:\"1. 克隆仓库\"}),\"\\n\",e(l.pre,{children:e(l.code,{className:\"language-bash\",children:\"git clone https://github.com/shipany/shipany-template\\r\\ncd shipany-template\\n\"})}),\"\\n\",e(l.h3,{children:\"2. 安装依赖\"}),\"\\n\",e(l.pre,{children:e(l.code,{className:\"language-bash\",children:\"pnpm install\\n\"})}),\"\\n\",e(l.h3,{children:\"3. 设置环境变量\"}),\"\\n\",r(l.p,{children:[\"创建一个 \",e(l.code,{children:\".env.local\"}),\" 文件并配置：\"]}),\"\\n\",e(l.pre,{children:e(l.code,{className:\"language-env\",children:\"NEXT_PUBLIC_WEB_URL=http://localhost:3000\\r\\nDATABASE_URL=your_database_url\\r\\nNEXTAUTH_SECRET=your_secret\\r\\nSTRIPE_SECRET_KEY=your_stripe_key\\n\"})}),\"\\n\",e(l.h3,{children:\"4. 运行开发服务器\"}),\"\\n\",e(l.pre,{children:e(l.code,{className:\"language-bash\",children:\"pnpm dev\\n\"})}),\"\\n\",e(l.h2,{children:\"构建你的第一个功能\"}),\"\\n\",e(l.p,{children:\"让我们创建一个简单的 AI 驱动的文本生成器：\"}),\"\\n\",e(l.h3,{children:\"1. 创建 API 路由\"}),\"\\n\",e(l.pre,{children:e(l.code,{className:\"language-typescript\",children:\"// app/api/generate/route.ts\\r\\nimport { openai } from '@ai-sdk/openai'\\r\\nimport { generateText } from 'ai'\\r\\n\\r\\nexport async function POST(request: Request) {\\r\\n  const { prompt } = await request.json()\\r\\n  \\r\\n  const { text } = await generateText({\\r\\n    model: openai('gpt-3.5-turbo'),\\r\\n    prompt: `基于以下内容生成创意文本：${prompt}`,\\r\\n  })\\r\\n  \\r\\n  return Response.json({ text })\\r\\n}\\n\"})}),\"\\n\",e(l.h3,{children:\"2. 创建前端组件\"}),\"\\n\",e(l.pre,{children:e(l.code,{className:\"language-tsx\",children:\"'use client'\\r\\n\\r\\nimport { useState } from 'react'\\r\\nimport { Button } from '@/components/ui/button'\\r\\nimport { Textarea } from '@/components/ui/textarea'\\r\\n\\r\\nexport function TextGenerator() {\\r\\n  const [prompt, setPrompt] = useState('')\\r\\n  const [result, setResult] = useState('')\\r\\n  const [loading, setLoading] = useState(false)\\r\\n\\r\\n  const handleGenerate = async () => {\\r\\n    setLoading(true)\\r\\n    try {\\r\\n      const response = await fetch('/api/generate', {\\r\\n        method: 'POST',\\r\\n        headers: { 'Content-Type': 'application/json' },\\r\\n        body: JSON.stringify({ prompt }),\\r\\n      })\\r\\n      const data = await response.json()\\r\\n      setResult(data.text)\\r\\n    } catch (error) {\\r\\n      console.error('错误:', error)\\r\\n    } finally {\\r\\n      setLoading(false)\\r\\n    }\\r\\n  }\\r\\n\\r\\n  return (\\r\\n    <div className=\\\"space-y-4\\\">\\r\\n      <Textarea\\r\\n        placeholder=\\\"输入你的提示...\\\"\\r\\n        value={prompt}\\r\\n        onChange={(e) => setPrompt(e.target.value)}\\r\\n      />\\r\\n      <Button onClick={handleGenerate} disabled={loading}>\\r\\n        {loading ? '生成中...' : '生成'}\\r\\n      </Button>\\r\\n      {result && (\\r\\n        <div className=\\\"p-4 bg-muted rounded-lg\\\">\\r\\n          {result}\\r\\n        </div>\\r\\n      )}\\r\\n    </div>\\r\\n  )\\r\\n}\\n\"})}),\"\\n\",e(l.h2,{children:\"下一步\"}),\"\\n\",e(l.p,{children:\"现在你已经设置好了基础，你可以：\"}),\"\\n\",r(l.ol,{children:[\"\\n\",r(l.li,{children:[e(l.strong,{children:\"自定义 UI\"}),\"：修改组件以匹配你的品牌\"]}),\"\\n\",r(l.li,{children:[e(l.strong,{children:\"添加更多 AI 功能\"}),\"：集成额外的 AI 模型\"]}),\"\\n\",r(l.li,{children:[e(l.strong,{children:\"设置支付\"}),\"：为订阅配置 Stripe\"]}),\"\\n\",r(l.li,{children:[e(l.strong,{children:\"部署\"}),\"：部署到 Vercel 或你首选的平台\"]}),\"\\n\"]}),\"\\n\",e(l.h2,{children:\"结论\"}),\"\\n\",e(l.p,{children:\"ShipAny 提供了快速构建和启动 AI SaaS 所需的一切。凭借其全面的功能集和开发者友好的架构，你可以专注于最重要的事情：为用户构建优秀的产品。\"}),\"\\n\",e(l.p,{children:\"准备好发布你的下一个 AI SaaS 了吗？今天就开始使用 ShipAny 吧！\"})]})}return{default:function(n={}){const{wrapper:r}=n.components||{};return r?e(r,{...n,children:e(_createMdxContent,{...n})}):_createMdxContent(n)}};", "excerpt": "ShipAny 快速入门\n欢迎使用 ShipAny！这份综合指南将带你使用我们强大的模板系统构建你的第一个 AI SaaS 应用程序。\n什么是 ShipAny？\nShipAny 是一个专为快速高效构建 AI SaaS 创业项目而设计的 Next.js 样板。通过预构建的组件、身份验证、支付处理和 AI 集成，你可以专注", "metadata": {"readingTime": 2, "wordCount": 439.36}, "createdAt": "2025-07-21T08:42:01.062Z"}, {"title": "English Only Test Article", "slug": "english-only-test", "description": "This article is only available in English to test the language switching fallback functionality.", "coverImage": "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=400&fit=crop", "author": "Test Author", "authorImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face", "publishedAt": "2025-01-17T00:00:00.000Z", "featured": false, "tags": ["test", "english-only", "language-switching"], "lang": "en", "url": "/D:\\VSCodeWorkspace\\shipany-stwd\\content\\blogs\\en\\english-only-test.mdx/", "content": "const{Fragment:e,jsx:n,jsxs:i}=arguments[0];function _createMdxContent(t){const r={code:\"code\",h1:\"h1\",h2:\"h2\",li:\"li\",ol:\"ol\",p:\"p\",strong:\"strong\",ul:\"ul\",...t.components};return i(e,{children:[n(r.h1,{children:\"English Only Test Article\"}),\"\\n\",n(r.p,{children:\"This article is specifically created to test the language switching functionality when content is not available in all languages.\"}),\"\\n\",n(r.h2,{children:\"Purpose\"}),\"\\n\",n(r.p,{children:\"When a user tries to switch to Chinese (中文) while viewing this article, they should be redirected to the Chinese blog list page since this article doesn't have a Chinese version.\"}),\"\\n\",n(r.h2,{children:\"Testing Scenarios\"}),\"\\n\",i(r.ol,{children:[\"\\n\",i(r.li,{children:[n(r.strong,{children:\"Direct Language Switch\"}),\": Use the header language selector\"]}),\"\\n\",i(r.li,{children:[n(r.strong,{children:\"Language Versions Indicator\"}),\": Use the language versions component on this page\"]}),\"\\n\",i(r.li,{children:[n(r.strong,{children:\"Fallback Behavior\"}),\": Verify that users are redirected appropriately\"]}),\"\\n\"]}),\"\\n\",n(r.h2,{children:\"Expected Behavior\"}),\"\\n\",i(r.ul,{children:[\"\\n\",n(r.li,{children:\"✅ English version should be accessible\"}),\"\\n\",n(r.li,{children:\"❌ Chinese version should not exist\"}),\"\\n\",i(r.li,{children:[\"🔄 Switching to Chinese should redirect to \",n(r.code,{children:\"/zh/blogs\"}),\" with a notification\"]}),\"\\n\"]}),\"\\n\",n(r.p,{children:\"This helps ensure our intelligent language switching system works correctly in all scenarios.\"})]})}return{default:function(e={}){const{wrapper:i}=e.components||{};return i?n(i,{...e,children:n(_createMdxContent,{...e})}):_createMdxContent(e)}};", "excerpt": "English Only Test Article\nThis article is specifically created to test the language switching functionality when content is not available in all languages.\nPurp", "metadata": {"readingTime": 1, "wordCount": 120.12}, "createdAt": "2025-07-21T08:42:01.051Z"}, {"title": "Getting Started with ShipAny: Build Your AI SaaS in Hours", "slug": "getting-started-with-shipany", "description": "Learn how to quickly build and deploy your AI SaaS application using ShipAny's powerful template and components.", "coverImage": "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop", "author": "ShipAny Team", "authorImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face", "publishedAt": "2025-01-17T00:00:00.000Z", "featured": true, "tags": ["tutorial", "getting-started", "ai-saas"], "lang": "en", "url": "/D:\\VSCodeWorkspace\\shipany-stwd\\content\\blogs\\en\\getting-started-with-shipany.mdx/", "content": "const{Fragment:e,jsx:n,jsxs:r}=arguments[0];function _createMdxContent(t){const o={code:\"code\",h1:\"h1\",h2:\"h2\",h3:\"h3\",li:\"li\",ol:\"ol\",p:\"p\",pre:\"pre\",strong:\"strong\",ul:\"ul\",...t.components};return r(e,{children:[n(o.h1,{children:\"Getting Started with ShipAny\"}),\"\\n\",n(o.p,{children:\"Welcome to ShipAny! This comprehensive guide will walk you through building your first AI SaaS application using our powerful template system.\"}),\"\\n\",n(o.h2,{children:\"What is ShipAny?\"}),\"\\n\",n(o.p,{children:\"ShipAny is a Next.js boilerplate designed specifically for building AI SaaS startups quickly and efficiently. With pre-built components, authentication, payment processing, and AI integrations, you can focus on your unique value proposition rather than boilerplate code.\"}),\"\\n\",n(o.h2,{children:\"Key Features\"}),\"\\n\",r(o.ul,{children:[\"\\n\",r(o.li,{children:[n(o.strong,{children:\"🚀 Rapid Development\"}),\": Pre-built components and templates\"]}),\"\\n\",r(o.li,{children:[n(o.strong,{children:\"🤖 AI Integration\"}),\": Ready-to-use AI SDK integrations\"]}),\"\\n\",r(o.li,{children:[n(o.strong,{children:\"💳 Payment Processing\"}),\": Stripe integration out of the box\"]}),\"\\n\",r(o.li,{children:[n(o.strong,{children:\"🌍 Internationalization\"}),\": Multi-language support\"]}),\"\\n\",r(o.li,{children:[n(o.strong,{children:\"📱 Responsive Design\"}),\": Mobile-first approach\"]}),\"\\n\",r(o.li,{children:[n(o.strong,{children:\"🔐 Authentication\"}),\": Secure user management\"]}),\"\\n\"]}),\"\\n\",n(o.h2,{children:\"Quick Start\"}),\"\\n\",n(o.h3,{children:\"1. Clone the Repository\"}),\"\\n\",n(o.pre,{children:n(o.code,{className:\"language-bash\",children:\"git clone https://github.com/shipany/shipany-template\\r\\ncd shipany-template\\n\"})}),\"\\n\",n(o.h3,{children:\"2. Install Dependencies\"}),\"\\n\",n(o.pre,{children:n(o.code,{className:\"language-bash\",children:\"pnpm install\\n\"})}),\"\\n\",n(o.h3,{children:\"3. Set Up Environment Variables\"}),\"\\n\",r(o.p,{children:[\"Create a \",n(o.code,{children:\".env.local\"}),\" file with your configuration:\"]}),\"\\n\",n(o.pre,{children:n(o.code,{className:\"language-env\",children:\"NEXT_PUBLIC_WEB_URL=http://localhost:3000\\r\\nDATABASE_URL=your_database_url\\r\\nNEXTAUTH_SECRET=your_secret\\r\\nSTRIPE_SECRET_KEY=your_stripe_key\\n\"})}),\"\\n\",n(o.h3,{children:\"4. Run the Development Server\"}),\"\\n\",n(o.pre,{children:n(o.code,{className:\"language-bash\",children:\"pnpm dev\\n\"})}),\"\\n\",n(o.h2,{children:\"Building Your First Feature\"}),\"\\n\",n(o.p,{children:\"Let's create a simple AI-powered text generator:\"}),\"\\n\",n(o.h3,{children:\"1. Create the API Route\"}),\"\\n\",n(o.pre,{children:n(o.code,{className:\"language-typescript\",children:\"// app/api/generate/route.ts\\r\\nimport { openai } from '@ai-sdk/openai'\\r\\nimport { generateText } from 'ai'\\r\\n\\r\\nexport async function POST(request: Request) {\\r\\n  const { prompt } = await request.json()\\r\\n  \\r\\n  const { text } = await generateText({\\r\\n    model: openai('gpt-3.5-turbo'),\\r\\n    prompt: `Generate creative content based on: ${prompt}`,\\r\\n  })\\r\\n  \\r\\n  return Response.json({ text })\\r\\n}\\n\"})}),\"\\n\",n(o.h3,{children:\"2. Create the Frontend Component\"}),\"\\n\",n(o.pre,{children:n(o.code,{className:\"language-tsx\",children:\"'use client'\\r\\n\\r\\nimport { useState } from 'react'\\r\\nimport { Button } from '@/components/ui/button'\\r\\nimport { Textarea } from '@/components/ui/textarea'\\r\\n\\r\\nexport function TextGenerator() {\\r\\n  const [prompt, setPrompt] = useState('')\\r\\n  const [result, setResult] = useState('')\\r\\n  const [loading, setLoading] = useState(false)\\r\\n\\r\\n  const handleGenerate = async () => {\\r\\n    setLoading(true)\\r\\n    try {\\r\\n      const response = await fetch('/api/generate', {\\r\\n        method: 'POST',\\r\\n        headers: { 'Content-Type': 'application/json' },\\r\\n        body: JSON.stringify({ prompt }),\\r\\n      })\\r\\n      const data = await response.json()\\r\\n      setResult(data.text)\\r\\n    } catch (error) {\\r\\n      console.error('Error:', error)\\r\\n    } finally {\\r\\n      setLoading(false)\\r\\n    }\\r\\n  }\\r\\n\\r\\n  return (\\r\\n    <div className=\\\"space-y-4\\\">\\r\\n      <Textarea\\r\\n        placeholder=\\\"Enter your prompt...\\\"\\r\\n        value={prompt}\\r\\n        onChange={(e) => setPrompt(e.target.value)}\\r\\n      />\\r\\n      <Button onClick={handleGenerate} disabled={loading}>\\r\\n        {loading ? 'Generating...' : 'Generate'}\\r\\n      </Button>\\r\\n      {result && (\\r\\n        <div className=\\\"p-4 bg-muted rounded-lg\\\">\\r\\n          {result}\\r\\n        </div>\\r\\n      )}\\r\\n    </div>\\r\\n  )\\r\\n}\\n\"})}),\"\\n\",n(o.h2,{children:\"Next Steps\"}),\"\\n\",n(o.p,{children:\"Now that you have the basics set up, you can:\"}),\"\\n\",r(o.ol,{children:[\"\\n\",r(o.li,{children:[n(o.strong,{children:\"Customize the UI\"}),\": Modify components to match your brand\"]}),\"\\n\",r(o.li,{children:[n(o.strong,{children:\"Add More AI Features\"}),\": Integrate additional AI models\"]}),\"\\n\",r(o.li,{children:[n(o.strong,{children:\"Set Up Payments\"}),\": Configure Stripe for subscriptions\"]}),\"\\n\",r(o.li,{children:[n(o.strong,{children:\"Deploy\"}),\": Deploy to Vercel or your preferred platform\"]}),\"\\n\"]}),\"\\n\",n(o.h2,{children:\"Conclusion\"}),\"\\n\",n(o.p,{children:\"ShipAny provides everything you need to build and launch your AI SaaS quickly. With its comprehensive feature set and developer-friendly architecture, you can focus on what matters most: building great products for your users.\"}),\"\\n\",n(o.p,{children:\"Ready to ship your next AI SaaS? Get started with ShipAny today!\"})]})}return{default:function(e={}){const{wrapper:r}=e.components||{};return r?n(r,{...e,children:n(_createMdxContent,{...e})}):_createMdxContent(e)}};", "excerpt": "Getting Started with ShipAny\nWelcome to ShipAny! This comprehensive guide will walk you through building your first AI SaaS application using our powerful templ", "metadata": {"readingTime": 2, "wordCount": 432}, "createdAt": "2025-07-21T08:42:01.067Z"}]