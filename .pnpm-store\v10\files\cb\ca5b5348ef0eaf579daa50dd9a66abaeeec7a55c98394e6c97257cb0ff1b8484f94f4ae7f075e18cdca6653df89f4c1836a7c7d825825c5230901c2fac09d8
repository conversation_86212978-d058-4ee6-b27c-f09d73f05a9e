{"version": 3, "file": "rules.d.ts", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/dlp/email/rules.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AAEpD,qBAAa,KAAM,SAAQ,WAAW;IACpC;;;;;;;;;;;;;;;;;;;OAmBG;IACH,MAAM,CAAC,MAAM,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;IASpG;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,MAAM,CACJ,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,gBAAgB,EACxB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;IAUtC;;;;;;;;;;;;OAYG;IACH,IAAI,CACF,MAAM,EAAE,cAAc,EACtB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,WAAW,CAAC,2BAA2B,EAAE,gBAAgB,CAAC;IASlE;;;;;;;;;;OAUG;IACH,MAAM,CACJ,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,gBAAgB,EACxB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;IAStC;;;;;;;;;;;OAWG;IACH,QAAQ,CAAC,MAAM,EAAE,kBAAkB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC;IAS1G;;;;;;;;;;OAUG;IACH,GAAG,CACD,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,aAAa,EACrB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;CAQpC;AAED,qBAAa,2BAA4B,SAAQ,UAAU,CAAC,gBAAgB,CAAC;CAAG;AAEhF,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,kBAAkB,CAAC,MAAM,CAAC;IAElC;;OAEG;IACH,UAAU,EAAE,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAEhD,UAAU,EAAE,MAAM,CAAC;IAEnB,OAAO,EAAE,OAAO,CAAC;IAEjB,IAAI,EAAE,MAAM,CAAC;IAEb,QAAQ,EAAE,MAAM,CAAC;IAEjB,OAAO,EAAE,MAAM,CAAC;IAEhB,UAAU,EAAE,MAAM,CAAC;IAEnB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B;AAED,yBAAiB,kBAAkB,CAAC;IAClC,UAAiB,MAAM;QACrB,MAAM,EAAE,OAAO,CAAC;QAEhB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,SAAS;QACxB,QAAQ,EAAE,QAAQ,GAAG,WAAW,GAAG,YAAY,GAAG,eAAe,CAAC;QAElE,QAAQ,EAAE,YAAY,GAAG,QAAQ,GAAG,aAAa,CAAC;QAElD,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;KAC/B;CACF;AAED,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,kBAAkB,CAAC,MAAM,CAAC;IAElC;;OAEG;IACH,UAAU,EAAE,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAEhD,UAAU,EAAE,MAAM,CAAC;IAEnB,OAAO,EAAE,OAAO,CAAC;IAEjB,IAAI,EAAE,MAAM,CAAC;IAEb,QAAQ,EAAE,MAAM,CAAC;IAEjB,OAAO,EAAE,MAAM,CAAC;IAEhB,UAAU,EAAE,MAAM,CAAC;IAEnB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B;AAED,yBAAiB,kBAAkB,CAAC;IAClC,UAAiB,MAAM;QACrB,MAAM,EAAE,OAAO,CAAC;QAEhB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,SAAS;QACxB,QAAQ,EAAE,QAAQ,GAAG,WAAW,GAAG,YAAY,GAAG,eAAe,CAAC;QAElE,QAAQ,EAAE,YAAY,GAAG,QAAQ,GAAG,aAAa,CAAC;QAElD,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;KAC/B;CACF;AAED,MAAM,WAAW,gBAAgB;IAC/B,MAAM,EAAE,gBAAgB,CAAC,MAAM,CAAC;IAEhC;;OAEG;IACH,UAAU,EAAE,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAE9C,UAAU,EAAE,MAAM,CAAC;IAEnB,OAAO,EAAE,OAAO,CAAC;IAEjB,IAAI,EAAE,MAAM,CAAC;IAEb,QAAQ,EAAE,MAAM,CAAC;IAEjB,OAAO,EAAE,MAAM,CAAC;IAEhB,UAAU,EAAE,MAAM,CAAC;IAEnB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B;AAED,yBAAiB,gBAAgB,CAAC;IAChC,UAAiB,MAAM;QACrB,MAAM,EAAE,OAAO,CAAC;QAEhB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,SAAS;QACxB,QAAQ,EAAE,QAAQ,GAAG,WAAW,GAAG,YAAY,GAAG,eAAe,CAAC;QAElE,QAAQ,EAAE,YAAY,GAAG,QAAQ,GAAG,aAAa,CAAC;QAElD,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;KAC/B;CACF;AAED,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,kBAAkB,CAAC,MAAM,CAAC;IAElC;;OAEG;IACH,UAAU,EAAE,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAEhD,UAAU,EAAE,MAAM,CAAC;IAEnB,OAAO,EAAE,OAAO,CAAC;IAEjB,IAAI,EAAE,MAAM,CAAC;IAEb,QAAQ,EAAE,MAAM,CAAC;IAEjB,OAAO,EAAE,MAAM,CAAC;IAEhB,UAAU,EAAE,MAAM,CAAC;IAEnB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B;AAED,yBAAiB,kBAAkB,CAAC;IAClC,UAAiB,MAAM;QACrB,MAAM,EAAE,OAAO,CAAC;QAEhB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,SAAS;QACxB,QAAQ,EAAE,QAAQ,GAAG,WAAW,GAAG,YAAY,GAAG,eAAe,CAAC;QAElE,QAAQ,EAAE,YAAY,GAAG,QAAQ,GAAG,aAAa,CAAC;QAElD,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;KAC/B;CACF;AAED,MAAM,WAAW,oBAAoB;IACnC,MAAM,EAAE,oBAAoB,CAAC,MAAM,CAAC;IAEpC;;OAEG;IACH,UAAU,EAAE,KAAK,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;IAElD,UAAU,EAAE,MAAM,CAAC;IAEnB,OAAO,EAAE,OAAO,CAAC;IAEjB,IAAI,EAAE,MAAM,CAAC;IAEb,QAAQ,EAAE,MAAM,CAAC;IAEjB,OAAO,EAAE,MAAM,CAAC;IAEhB,UAAU,EAAE,MAAM,CAAC;IAEnB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B;AAED,yBAAiB,oBAAoB,CAAC;IACpC,UAAiB,MAAM;QACrB,MAAM,EAAE,OAAO,CAAC;QAEhB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,SAAS;QACxB,QAAQ,EAAE,QAAQ,GAAG,WAAW,GAAG,YAAY,GAAG,eAAe,CAAC;QAElE,QAAQ,EAAE,YAAY,GAAG,QAAQ,GAAG,aAAa,CAAC;QAElD,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;KAC/B;CACF;AAED,MAAM,WAAW,eAAe;IAC9B,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC;IAE/B;;OAEG;IACH,UAAU,EAAE,KAAK,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IAE7C,UAAU,EAAE,MAAM,CAAC;IAEnB,OAAO,EAAE,OAAO,CAAC;IAEjB,IAAI,EAAE,MAAM,CAAC;IAEb,QAAQ,EAAE,MAAM,CAAC;IAEjB,OAAO,EAAE,MAAM,CAAC;IAEhB,UAAU,EAAE,MAAM,CAAC;IAEnB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B;AAED,yBAAiB,eAAe,CAAC;IAC/B,UAAiB,MAAM;QACrB,MAAM,EAAE,OAAO,CAAC;QAEhB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,SAAS;QACxB,QAAQ,EAAE,QAAQ,GAAG,WAAW,GAAG,YAAY,GAAG,eAAe,CAAC;QAElE,QAAQ,EAAE,YAAY,GAAG,QAAQ,GAAG,aAAa,CAAC;QAElD,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;KAC/B;CACF;AAED,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,MAAM,EAAE,gBAAgB,CAAC,MAAM,CAAC;IAEhC;;OAEG;IACH,UAAU,EAAE,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAE9C;;OAEG;IACH,OAAO,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B;AAED,yBAAiB,gBAAgB,CAAC;IAChC,UAAiB,MAAM;QACrB,MAAM,EAAE,OAAO,CAAC;QAEhB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,SAAS;QACxB,QAAQ,EAAE,QAAQ,GAAG,WAAW,GAAG,YAAY,GAAG,eAAe,CAAC;QAElE,QAAQ,EAAE,YAAY,GAAG,QAAQ,GAAG,aAAa,CAAC;QAElD,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;KAC/B;CACF;AAED,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,MAAM,EAAE,gBAAgB,CAAC,MAAM,CAAC;IAEhC;;OAEG;IACH,UAAU,EAAE,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAE9C;;OAEG;IACH,OAAO,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B;AAED,yBAAiB,gBAAgB,CAAC;IAChC,UAAiB,MAAM;QACrB,MAAM,EAAE,OAAO,CAAC;QAEhB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,UAAiB,SAAS;QACxB,QAAQ,EAAE,QAAQ,GAAG,WAAW,GAAG,YAAY,GAAG,eAAe,CAAC;QAElE,QAAQ,EAAE,YAAY,GAAG,QAAQ,GAAG,aAAa,CAAC;QAElD,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;KAC/B;CACF;AAED,MAAM,WAAW,cAAc;IAC7B,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,gBAAgB;IAC/B,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,cAAc,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;CAC3C;AAED,MAAM,WAAW,aAAa;IAC5B,UAAU,EAAE,MAAM,CAAC;CACpB;AAID,MAAM,CAAC,OAAO,WAAW,KAAK,CAAC;IAC7B,OAAO,EACL,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,eAAe,IAAI,eAAe,EACvC,2BAA2B,IAAI,2BAA2B,EAC1D,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,cAAc,IAAI,cAAc,EACrC,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,aAAa,IAAI,aAAa,GACpC,CAAC;CACH"}