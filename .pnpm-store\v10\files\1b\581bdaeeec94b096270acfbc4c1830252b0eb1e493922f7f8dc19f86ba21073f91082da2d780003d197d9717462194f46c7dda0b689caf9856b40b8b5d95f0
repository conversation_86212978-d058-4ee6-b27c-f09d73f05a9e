// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import * as AccountMappingAPI from "./account-mapping.mjs";
import { AccountMapping, } from "./account-mapping.mjs";
import * as RulesAPI from "./rules.mjs";
import { RuleListResponsesSinglePage, Rules, } from "./rules.mjs";
export class Email extends APIResource {
    constructor() {
        super(...arguments);
        this.accountMapping = new AccountMappingAPI.AccountMapping(this._client);
        this.rules = new RulesAPI.Rules(this._client);
    }
}
Email.AccountMapping = AccountMapping;
Email.Rules = Rules;
Email.RuleListResponsesSinglePage = RuleListResponsesSinglePage;
//# sourceMappingURL=email.mjs.map