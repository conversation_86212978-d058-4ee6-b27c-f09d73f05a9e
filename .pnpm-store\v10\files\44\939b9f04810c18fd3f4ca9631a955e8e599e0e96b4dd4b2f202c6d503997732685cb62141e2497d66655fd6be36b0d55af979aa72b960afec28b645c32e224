import { APIResource } from "../../../../resource.js";
import * as Core from "../../../../core.js";
import * as SummaryAPI from "./summary.js";
import { Summary, SummaryHTTPMethodParams, SummaryHTTPMethodResponse, SummaryHTTPVersionParams, SummaryHTTPVersionResponse, SummaryIPVersionParams, SummaryIPVersionResponse, SummaryIndustryParams, SummaryIndustryResponse, SummaryManagedRulesParams, SummaryManagedRulesResponse, SummaryMitigationProductParams, SummaryMitigationProductResponse, SummaryVerticalParams, SummaryVerticalResponse } from "./summary.js";
import * as TimeseriesGroupsAPI from "./timeseries-groups.js";
import { TimeseriesGroupHTTPMethodParams, TimeseriesGroupHTTPMethodResponse, TimeseriesGroupHTTPVersionParams, TimeseriesGroupHTTPVersionResponse, TimeseriesGroupIPVersionParams, TimeseriesGroupIPVersionResponse, TimeseriesGroupIndustryParams, TimeseriesGroupIndustryResponse, TimeseriesGroupManagedRulesParams, TimeseriesGroupManagedRulesResponse, TimeseriesGroupMitigationProductParams, TimeseriesGroupMitigationProductResponse, TimeseriesGroupVerticalParams, TimeseriesGroupVerticalResponse, TimeseriesGroups } from "./timeseries-groups.js";
import * as TopAPI from "./top/top.js";
import { Top, TopAttacksParams, TopAttacksResponse, TopIndustryParams, TopIndustryResponse, TopVerticalParams, TopVerticalResponse } from "./top/top.js";
export declare class Layer7 extends APIResource {
    summary: SummaryAPI.Summary;
    timeseriesGroups: TimeseriesGroupsAPI.TimeseriesGroups;
    top: TopAPI.Top;
    /**
     * Retrieves layer 7 attacks over time.
     *
     * @example
     * ```ts
     * const response =
     *   await client.radar.attacks.layer7.timeseries();
     * ```
     */
    timeseries(query?: Layer7TimeseriesParams, options?: Core.RequestOptions): Core.APIPromise<Layer7TimeseriesResponse>;
    timeseries(options?: Core.RequestOptions): Core.APIPromise<Layer7TimeseriesResponse>;
}
export interface Layer7TimeseriesResponse {
    /**
     * Metadata for the results.
     */
    meta: Layer7TimeseriesResponse.Meta;
    serie_0: Layer7TimeseriesResponse.Serie0;
}
export declare namespace Layer7TimeseriesResponse {
    /**
     * Metadata for the results.
     */
    interface Meta {
        /**
         * Aggregation interval of the results (e.g., in 15 minutes or 1 hour intervals).
         * Refer to
         * [Aggregation intervals](https://developers.cloudflare.com/radar/concepts/aggregation-intervals/).
         */
        aggInterval: 'FIFTEEN_MINUTES' | 'ONE_HOUR' | 'ONE_DAY' | 'ONE_WEEK' | 'ONE_MONTH';
        confidenceInfo: Meta.ConfidenceInfo;
        dateRange: Array<Meta.DateRange>;
        /**
         * Timestamp of the last dataset update.
         */
        lastUpdated: string;
        /**
         * Normalization method applied to the results. Refer to
         * [Normalization methods](https://developers.cloudflare.com/radar/concepts/normalization/).
         */
        normalization: 'PERCENTAGE' | 'MIN0_MAX' | 'MIN_MAX' | 'RAW_VALUES' | 'PERCENTAGE_CHANGE' | 'ROLLING_AVERAGE' | 'OVERLAPPED_PERCENTAGE' | 'RATIO';
        /**
         * Measurement units for the results.
         */
        units: Array<Meta.Unit>;
    }
    namespace Meta {
        interface ConfidenceInfo {
            annotations: Array<ConfidenceInfo.Annotation>;
            /**
             * Provides an indication of how much confidence Cloudflare has in the data.
             */
            level: number;
        }
        namespace ConfidenceInfo {
            /**
             * Annotation associated with the result (e.g. outage or other type of event).
             */
            interface Annotation {
                dataSource: string;
                description: string;
                endDate: string;
                eventType: string;
                /**
                 * Whether event is a single point in time or a time range.
                 */
                isInstantaneous: boolean;
                linkedUrl: string;
                startDate: string;
            }
        }
        interface DateRange {
            /**
             * Adjusted end of date range.
             */
            endTime: string;
            /**
             * Adjusted start of date range.
             */
            startTime: string;
        }
        interface Unit {
            name: string;
            value: string;
        }
    }
    interface Serie0 {
        timestamps: Array<string>;
        values: Array<string>;
    }
}
export interface Layer7TimeseriesParams {
    /**
     * Aggregation interval of the results (e.g., in 15 minutes or 1 hour intervals).
     * Refer to
     * [Aggregation intervals](https://developers.cloudflare.com/radar/concepts/aggregation-intervals/).
     */
    aggInterval?: '15m' | '1h' | '1d' | '1w';
    /**
     * Filters results by Autonomous System. Specify one or more Autonomous System
     * Numbers (ASNs) as a comma-separated list. Prefix with `-` to exclude ASNs from
     * results. For example, `-174, 3356` excludes results from AS174, but includes
     * results from AS3356.
     */
    asn?: Array<string>;
    /**
     * Filters results by continent. Specify a comma-separated list of alpha-2 codes.
     * Prefix with `-` to exclude continents from results. For example, `-EU,NA`
     * excludes results from EU, but includes results from NA.
     */
    continent?: Array<string>;
    /**
     * End of the date range (inclusive).
     */
    dateEnd?: Array<string>;
    /**
     * Filters results by date range. For example, use `7d` and `7dcontrol` to compare
     * this week with the previous week. Use this parameter or set specific start and
     * end dates (`dateStart` and `dateEnd` parameters).
     */
    dateRange?: Array<string>;
    /**
     * Start of the date range.
     */
    dateStart?: Array<string>;
    /**
     * Format in which results will be returned.
     */
    format?: 'JSON' | 'CSV';
    /**
     * Filters results by HTTP method.
     */
    httpMethod?: Array<'GET' | 'POST' | 'DELETE' | 'PUT' | 'HEAD' | 'PURGE' | 'OPTIONS' | 'PROPFIND' | 'MKCOL' | 'PATCH' | 'ACL' | 'BCOPY' | 'BDELETE' | 'BMOVE' | 'BPROPFIND' | 'BPROPPATCH' | 'CHECKIN' | 'CHECKOUT' | 'CONNECT' | 'COPY' | 'LABEL' | 'LOCK' | 'MERGE' | 'MKACTIVITY' | 'MKWORKSPACE' | 'MOVE' | 'NOTIFY' | 'ORDERPATCH' | 'POLL' | 'PROPPATCH' | 'REPORT' | 'SEARCH' | 'SUBSCRIBE' | 'TRACE' | 'UNCHECKOUT' | 'UNLOCK' | 'UNSUBSCRIBE' | 'UPDATE' | 'VERSIONCONTROL' | 'BASELINECONTROL' | 'XMSENUMATTS' | 'RPC_OUT_DATA' | 'RPC_IN_DATA' | 'JSON' | 'COOK' | 'TRACK'>;
    /**
     * Filters results by HTTP version.
     */
    httpVersion?: Array<'HTTPv1' | 'HTTPv2' | 'HTTPv3'>;
    /**
     * Filters results by IP version (Ipv4 vs. IPv6).
     */
    ipVersion?: Array<'IPv4' | 'IPv6'>;
    /**
     * Filters results by location. Specify a comma-separated list of alpha-2 codes.
     * Prefix with `-` to exclude locations from results. For example, `-US,PT`
     * excludes results from the US, but includes results from PT.
     */
    location?: Array<string>;
    /**
     * Filters the results by layer 7 mitigation product.
     */
    mitigationProduct?: Array<'DDOS' | 'WAF' | 'BOT_MANAGEMENT' | 'ACCESS_RULES' | 'IP_REPUTATION' | 'API_SHIELD' | 'DATA_LOSS_PREVENTION'>;
    /**
     * Array of names used to label the series in the response.
     */
    name?: Array<string>;
    /**
     * Normalization method applied to the results. Refer to
     * [Normalization methods](https://developers.cloudflare.com/radar/concepts/normalization/).
     */
    normalization?: 'PERCENTAGE_CHANGE' | 'MIN0_MAX';
}
export declare namespace Layer7 {
    export { type Layer7TimeseriesResponse as Layer7TimeseriesResponse, type Layer7TimeseriesParams as Layer7TimeseriesParams, };
    export { Summary as Summary, type SummaryHTTPMethodResponse as SummaryHTTPMethodResponse, type SummaryHTTPVersionResponse as SummaryHTTPVersionResponse, type SummaryIndustryResponse as SummaryIndustryResponse, type SummaryIPVersionResponse as SummaryIPVersionResponse, type SummaryManagedRulesResponse as SummaryManagedRulesResponse, type SummaryMitigationProductResponse as SummaryMitigationProductResponse, type SummaryVerticalResponse as SummaryVerticalResponse, type SummaryHTTPMethodParams as SummaryHTTPMethodParams, type SummaryHTTPVersionParams as SummaryHTTPVersionParams, type SummaryIndustryParams as SummaryIndustryParams, type SummaryIPVersionParams as SummaryIPVersionParams, type SummaryManagedRulesParams as SummaryManagedRulesParams, type SummaryMitigationProductParams as SummaryMitigationProductParams, type SummaryVerticalParams as SummaryVerticalParams, };
    export { TimeseriesGroups as TimeseriesGroups, type TimeseriesGroupHTTPMethodResponse as TimeseriesGroupHTTPMethodResponse, type TimeseriesGroupHTTPVersionResponse as TimeseriesGroupHTTPVersionResponse, type TimeseriesGroupIndustryResponse as TimeseriesGroupIndustryResponse, type TimeseriesGroupIPVersionResponse as TimeseriesGroupIPVersionResponse, type TimeseriesGroupManagedRulesResponse as TimeseriesGroupManagedRulesResponse, type TimeseriesGroupMitigationProductResponse as TimeseriesGroupMitigationProductResponse, type TimeseriesGroupVerticalResponse as TimeseriesGroupVerticalResponse, type TimeseriesGroupHTTPMethodParams as TimeseriesGroupHTTPMethodParams, type TimeseriesGroupHTTPVersionParams as TimeseriesGroupHTTPVersionParams, type TimeseriesGroupIndustryParams as TimeseriesGroupIndustryParams, type TimeseriesGroupIPVersionParams as TimeseriesGroupIPVersionParams, type TimeseriesGroupManagedRulesParams as TimeseriesGroupManagedRulesParams, type TimeseriesGroupMitigationProductParams as TimeseriesGroupMitigationProductParams, type TimeseriesGroupVerticalParams as TimeseriesGroupVerticalParams, };
    export { Top as Top, type TopAttacksResponse as TopAttacksResponse, type TopIndustryResponse as TopIndustryResponse, type TopVerticalResponse as TopVerticalResponse, type TopAttacksParams as TopAttacksParams, type TopIndustryParams as TopIndustryParams, type TopVerticalParams as TopVerticalParams, };
}
//# sourceMappingURL=layer7.d.ts.map