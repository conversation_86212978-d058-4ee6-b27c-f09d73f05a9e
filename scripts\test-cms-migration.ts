#!/usr/bin/env tsx

/**
 * CMS Migration Test Script
 * 
 * This script tests the CMS migration from Contentlayer to Contentlayer2
 * by verifying that all content operations work correctly with the new
 * provider and that the migration maintains data integrity.
 * 
 * Usage:
 *   pnpm tsx scripts/test-cms-migration.ts
 */

import { initializeCMS, cms } from '../src/cms/services'
import { seoService } from '../src/cms/services/seo'
import { cacheService } from '../src/cms/services/cache'
import type { ContentType } from '../src/cms/types'

// Test configuration
const TEST_CONFIG = {
  contentTypes: ['blog', 'product', 'case-study'] as ContentType[],
  locales: ['en', 'zh'],
  testSlugs: {
    blog: ['test-blog', 'another-blog'],
    product: ['test-product'],
    'case-study': ['test-case-study']
  }
}

// Test results tracking
interface TestResult {
  name: string
  passed: boolean
  error?: string
  duration: number
}

class CMSMigrationTester {
  private results: TestResult[] = []

  /**
   * Run a test and track the result
   */
  private async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now()
    
    try {
      await testFn()
      this.results.push({
        name,
        passed: true,
        duration: Date.now() - startTime
      })
      console.log(`✅ ${name}`)
    } catch (error) {
      this.results.push({
        name,
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      })
      console.log(`❌ ${name}: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * Test CMS initialization
   */
  private async testInitialization(): Promise<void> {
    await this.runTest('CMS Initialization', async () => {
      await initializeCMS({
        provider: 'contentlayer2',
        contentTypes: TEST_CONFIG.contentTypes,
        defaultLocale: 'en',
        supportedLocales: TEST_CONFIG.locales,
        features: {
          cache: true,
          seo: true,
          relatedContent: true,
          languageSwitching: true
        }
      })

      if (!cms.isInitialized()) {
        throw new Error('CMS failed to initialize')
      }

      const providerInfo = cms.getProviderInfo()
      if (providerInfo?.name !== 'contentlayer2') {
        throw new Error(`Expected contentlayer2 provider, got ${providerInfo?.name}`)
      }
    })
  }

  /**
   * Test content retrieval for all types and locales
   */
  private async testContentRetrieval(): Promise<void> {
    for (const contentType of TEST_CONFIG.contentTypes) {
      for (const locale of TEST_CONFIG.locales) {
        await this.runTest(`Get ${contentType} list (${locale})`, async () => {
          const content = await cms.getContentList(contentType, locale)
          
          if (!Array.isArray(content)) {
            throw new Error('Content list should be an array')
          }

          // Verify all items have correct locale
          for (const item of content) {
            if (item.lang !== locale) {
              throw new Error(`Content item has wrong locale: expected ${locale}, got ${item.lang}`)
            }
          }

          console.log(`  Found ${content.length} ${contentType} items in ${locale}`)
        })
      }
    }
  }

  /**
   * Test individual content item retrieval
   */
  private async testIndividualContent(): Promise<void> {
    for (const [contentType, slugs] of Object.entries(TEST_CONFIG.testSlugs)) {
      for (const slug of slugs) {
        for (const locale of TEST_CONFIG.locales) {
          await this.runTest(`Get ${contentType}/${slug} (${locale})`, async () => {
            const content = await cms.getContent(contentType as ContentType, slug, locale)
            
            if (content) {
              // Verify content structure
              if (!content.title || !content.slug || !content.lang || !content.url) {
                throw new Error('Content item missing required fields')
              }

              if (content.slug !== slug) {
                throw new Error(`Content slug mismatch: expected ${slug}, got ${content.slug}`)
              }

              if (content.lang !== locale) {
                throw new Error(`Content locale mismatch: expected ${locale}, got ${content.lang}`)
              }

              console.log(`  Found: ${content.title}`)
            } else {
              console.log(`  Not found (this may be expected)`)
            }
          })
        }
      }
    }
  }

  /**
   * Test content existence checks
   */
  private async testContentExistence(): Promise<void> {
    await this.runTest('Content existence checks', async () => {
      // Test existing content
      const exists = await cms.contentExists('blog', 'test-blog', 'en')
      console.log(`  test-blog exists: ${exists}`)

      // Test non-existing content
      const notExists = await cms.contentExists('blog', 'non-existent-slug', 'en')
      if (notExists) {
        throw new Error('Non-existent content reported as existing')
      }
    })
  }

  /**
   * Test content metadata extraction
   */
  private async testContentMetadata(): Promise<void> {
    await this.runTest('Content metadata extraction', async () => {
      const content = await cms.getContentList('blog', 'en', { limit: 1 })
      
      if (content.length === 0) {
        console.log('  No blog content found, skipping metadata test')
        return
      }

      const metadata = await cms.getContentMetadata('blog', content[0].slug, 'en')
      
      if (metadata) {
        if (typeof metadata.wordCount !== 'number' || metadata.wordCount < 0) {
          throw new Error('Invalid word count in metadata')
        }

        if (typeof metadata.readingTime !== 'number' || metadata.readingTime < 0) {
          throw new Error('Invalid reading time in metadata')
        }

        console.log(`  Word count: ${metadata.wordCount}, Reading time: ${metadata.readingTime}min`)
      }
    })
  }

  /**
   * Test language switching functionality
   */
  private async testLanguageSwitching(): Promise<void> {
    await this.runTest('Language switching', async () => {
      const content = await cms.getContentList('blog', 'en', { limit: 1 })
      
      if (content.length === 0) {
        console.log('  No blog content found, skipping language switching test')
        return
      }

      const languages = await cms.getAvailableLanguages('blog', content[0].slug)
      
      if (!Array.isArray(languages)) {
        throw new Error('Available languages should be an array')
      }

      console.log(`  Available languages: ${languages.map(l => l.lang).join(', ')}`)
    })
  }

  /**
   * Test related content functionality
   */
  private async testRelatedContent(): Promise<void> {
    await this.runTest('Related content', async () => {
      const content = await cms.getContentList('blog', 'en', { limit: 1 })
      
      if (content.length === 0) {
        console.log('  No blog content found, skipping related content test')
        return
      }

      const related = await cms.getRelatedContent('blog', content[0].slug, 'en', 3)
      
      if (!Array.isArray(related)) {
        throw new Error('Related content should be an array')
      }

      // Verify related content doesn't include the current item
      for (const item of related) {
        if (item.slug === content[0].slug) {
          throw new Error('Related content includes the current item')
        }
      }

      console.log(`  Found ${related.length} related items`)
    })
  }

  /**
   * Test SEO data generation
   */
  private async testSEOGeneration(): Promise<void> {
    await this.runTest('SEO data generation', async () => {
      const content = await cms.getContentList('blog', 'en', { limit: 1 })
      
      if (content.length === 0) {
        console.log('  No blog content found, skipping SEO test')
        return
      }

      const metaTags = await seoService.generateMetaTags(content[0], 'en')
      
      if (!metaTags.title || !metaTags.openGraph || !metaTags.twitter) {
        throw new Error('SEO meta tags missing required fields')
      }

      const structuredData = await seoService.generateStructuredData(content[0])
      
      if (!structuredData['@context'] || !structuredData['@type']) {
        throw new Error('Structured data missing required fields')
      }

      console.log(`  Generated meta tags and structured data for: ${content[0].title}`)
    })
  }

  /**
   * Test caching functionality
   */
  private async testCaching(): Promise<void> {
    await this.runTest('Cache operations', async () => {
      const content = await cms.getContentList('blog', 'en', { limit: 1 })
      
      if (content.length === 0) {
        console.log('  No blog content found, skipping cache test')
        return
      }

      const item = content[0]
      
      // Test caching
      await cacheService.cacheContent('blog', item.slug, 'en', item)
      
      // Test retrieval
      const cached = await cacheService.getCachedContent('blog', item.slug, 'en')
      
      if (!cached || cached.slug !== item.slug) {
        throw new Error('Failed to retrieve cached content')
      }

      // Test cache stats
      const stats = await cacheService.getStats()
      
      if (typeof stats.hits !== 'number' || typeof stats.misses !== 'number') {
        throw new Error('Invalid cache statistics')
      }

      console.log(`  Cache stats - Hits: ${stats.hits}, Misses: ${stats.misses}, Hit rate: ${(stats.hitRate * 100).toFixed(1)}%`)
    })
  }

  /**
   * Test query options and filtering
   */
  private async testQueryOptions(): Promise<void> {
    await this.runTest('Query options and filtering', async () => {
      // Test featured content filtering
      const featured = await cms.getContentList('blog', 'en', { featured: true })
      const nonFeatured = await cms.getContentList('blog', 'en', { featured: false })
      
      console.log(`  Featured: ${featured.length}, Non-featured: ${nonFeatured.length}`)

      // Test sorting
      const sorted = await cms.getContentList('blog', 'en', {
        sortBy: 'publishedAt',
        order: 'desc'
      })

      if (sorted.length > 1) {
        const first = new Date(sorted[0].publishedAt || sorted[0].createdAt)
        const second = new Date(sorted[1].publishedAt || sorted[1].createdAt)
        
        if (first.getTime() < second.getTime()) {
          throw new Error('Content not sorted correctly by publishedAt desc')
        }
      }

      // Test limit
      const limited = await cms.getContentList('blog', 'en', { limit: 1 })
      
      if (limited.length > 1) {
        throw new Error('Limit option not working correctly')
      }

      console.log(`  Query options working correctly`)
    })
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<void> {
    console.log('🚀 Starting CMS Migration Tests...\n')

    await this.testInitialization()
    await this.testContentRetrieval()
    await this.testIndividualContent()
    await this.testContentExistence()
    await this.testContentMetadata()
    await this.testLanguageSwitching()
    await this.testRelatedContent()
    await this.testSEOGeneration()
    await this.testCaching()
    await this.testQueryOptions()

    this.printSummary()
  }

  /**
   * Print test summary
   */
  private printSummary(): void {
    const passed = this.results.filter(r => r.passed).length
    const failed = this.results.filter(r => !r.passed).length
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0)

    console.log('\n📊 Test Summary:')
    console.log(`  Total tests: ${this.results.length}`)
    console.log(`  Passed: ${passed}`)
    console.log(`  Failed: ${failed}`)
    console.log(`  Total time: ${totalTime}ms`)
    console.log(`  Success rate: ${((passed / this.results.length) * 100).toFixed(1)}%`)

    if (failed > 0) {
      console.log('\n❌ Failed tests:')
      this.results
        .filter(r => !r.passed)
        .forEach(r => console.log(`  - ${r.name}: ${r.error}`))
    }

    if (failed === 0) {
      console.log('\n🎉 All tests passed! CMS migration is successful.')
    } else {
      console.log('\n⚠️  Some tests failed. Please review the errors above.')
      process.exit(1)
    }
  }
}

// Run the tests
async function main() {
  const tester = new CMSMigrationTester()
  await tester.runAllTests()
}

main().catch(error => {
  console.error('❌ Test script failed:', error)
  process.exit(1)
})
