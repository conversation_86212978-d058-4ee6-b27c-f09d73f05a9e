/**
 * Blogs List Page Component
 *
 * This page displays a grid of all blog posts for the current locale.
 * It includes SEO metadata generation, responsive design, and proper
 * internationalization support.
 */

import { allBlogs } from 'content-collections'
import { getTranslations, setRequestLocale } from 'next-intl/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Link } from '@/i18n/navigation'
import Image from 'next/image'
import { Metadata } from 'next'

/**
 * Generate SEO metadata for the blogs list page
 *
 * Creates appropriate title, description, and canonical URL based on
 * the current locale. English pages don't include locale in the URL.
 *
 * @param params - Route parameters containing locale
 * @returns Metadata object for Next.js
 */
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  setRequestLocale(locale)

  const t = await getTranslations()

  // Generate canonical URL with proper locale handling
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/blogs`

  if (locale !== 'en') {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/blogs`
  }

  return {
    title: t('blogs.title'),
    description: t('blogs.description'),
    alternates: {
      canonical: canonicalUrl,
    },
  }
}

/**
 * Main blogs list page component
 *
 * Renders a responsive grid of blog posts filtered by the current locale.
 * Posts are sorted by publication date (newest first) and include cover
 * images, metadata, and tag information.
 *
 * @param params - Route parameters containing locale
 * @returns JSX element for the blogs list page
 */
export default async function BlogsPage({
  params,
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  setRequestLocale(locale)

  const t = await getTranslations()

  // Filter blogs by current locale and sort by publication date (newest first)
  const blogs = allBlogs
    .filter(blog => blog.lang === locale)
    .sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime())

  return (
    <section className="py-16">
      <div className="container">
        <div className="mb-12 text-center">
          <h1 className="mb-4 text-4xl font-bold tracking-tight">
            {t('blogs.title')}
          </h1>
          <p className="text-xl text-muted-foreground">
            {t('blogs.description')}
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {blogs.map((blog) => (
            <Link key={blog.slug} href={`/blogs/${blog.slug}`}>
              <Card className="h-full transition-colors hover:bg-muted/50">
                {blog.coverImage && (
                  <div className="aspect-video overflow-hidden rounded-t-lg">
                    <Image
                      src={blog.coverImage}
                      alt={blog.title}
                      width={400}
                      height={225}
                      className="h-full w-full object-cover"
                    />
                  </div>
                )}
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="line-clamp-2">{blog.title}</CardTitle>
                    {blog.featured && (
                      <Badge variant="secondary">Featured</Badge>
                    )}
                  </div>
                  {blog.description && (
                    <CardDescription className="line-clamp-3">
                      {blog.description}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      {blog.author && <span>By {blog.author}</span>}
                      {blog.publishedAt && (
                        <time dateTime={blog.publishedAt}>
                          {new Date(blog.publishedAt).toLocaleDateString(locale, {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                          })}
                        </time>
                      )}
                    </div>
                  </div>
                  {blog.tags && blog.tags.length > 0 && (
                    <div className="mt-3 flex flex-wrap gap-2">
                      {blog.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {blogs.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              No blog posts found.
            </p>
          </div>
        )}
      </div>
    </section>
  )
}
