{"version": 3, "file": "users.d.ts", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/access/users/users.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AACzC,OAAO,KAAK,iBAAiB,MAAM,mBAAmB,CAAC;AACvD,OAAO,EACL,sBAAsB,EACtB,wBAAwB,EACxB,uBAAuB,EACvB,yBAAyB,EACzB,oCAAoC,EACpC,cAAc,EACf,MAAM,mBAAmB,CAAC;AAC3B,OAAO,KAAK,eAAe,MAAM,iBAAiB,CAAC;AACnD,OAAO,EACL,qBAAqB,EACrB,uBAAuB,EACvB,kCAAkC,EAClC,YAAY,EACb,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,mBAAmB,MAAM,sBAAsB,CAAC;AAC5D,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,yBAAyB,EAAE,MAAM,sBAAsB,CAAC;AAC7F,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AAEpD,qBAAa,KAAM,SAAQ,WAAW;IACpC,cAAc,EAAE,iBAAiB,CAAC,cAAc,CAAsD;IACtG,gBAAgB,EAAE,mBAAmB,CAAC,gBAAgB,CAEpD;IACF,YAAY,EAAE,eAAe,CAAC,YAAY,CAAkD;IAE5F;;;;;;;;;;;;OAYG;IACH,IAAI,CACF,MAAM,EAAE,cAAc,EACtB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,WAAW,CAAC,2BAA2B,EAAE,gBAAgB,CAAC;CAOnE;AAED,qBAAa,2BAA4B,SAAQ,UAAU,CAAC,gBAAgB,CAAC;CAAG;AAEhF,qBAAa,qBAAsB,SAAQ,UAAU,CAAC,UAAU,CAAC;CAAG;AAEpE,MAAM,WAAW,UAAU;IACzB;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAEjC;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,IAAI,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC;IAEvB;;OAEG;IACH,OAAO,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;CACzB;AAED,yBAAiB,UAAU,CAAC;IAC1B,UAAiB,KAAK;QACpB;;;WAGG;QACH,OAAO,CAAC,EAAE,OAAO,CAAC;QAElB;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;QAEd;;WAEG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;KAChB;IAED;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,CAAC;QAEjB;;WAEG;QACH,YAAY,CAAC,EAAE,MAAM,CAAC;KACvB;CACF;AAED,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IAEtB;;OAEG;IACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAE7B,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC;IAEvB;;OAEG;IACH,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAE/B;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IAEb,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,cAAc;IAC7B;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AASD,MAAM,CAAC,OAAO,WAAW,KAAK,CAAC;IAC7B,OAAO,EACL,KAAK,UAAU,IAAI,UAAU,EAC7B,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,2BAA2B,IAAI,2BAA2B,EAC1D,KAAK,cAAc,IAAI,cAAc,GACtC,CAAC;IAEF,OAAO,EACL,cAAc,IAAI,cAAc,EAChC,KAAK,yBAAyB,IAAI,yBAAyB,EAC3D,KAAK,wBAAwB,IAAI,wBAAwB,EACzD,oCAAoC,IAAI,oCAAoC,EAC5E,KAAK,uBAAuB,IAAI,uBAAuB,EACvD,KAAK,sBAAsB,IAAI,sBAAsB,GACtD,CAAC;IAEF,OAAO,EACL,gBAAgB,IAAI,gBAAgB,EACpC,KAAK,QAAQ,IAAI,QAAQ,EACzB,KAAK,yBAAyB,IAAI,yBAAyB,GAC5D,CAAC;IAEF,OAAO,EACL,YAAY,IAAI,YAAY,EAC5B,KAAK,uBAAuB,IAAI,uBAAuB,EACvD,kCAAkC,IAAI,kCAAkC,EACxE,KAAK,qBAAqB,IAAI,qBAAqB,GACpD,CAAC;CACH"}