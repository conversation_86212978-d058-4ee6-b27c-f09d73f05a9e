"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeseriesGroups = void 0;
const resource_1 = require("../../../../resource.js");
const core_1 = require("../../../../core.js");
class TimeseriesGroups extends resource_1.APIResource {
    arc(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.arc({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/arc', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    dkim(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.dkim({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/dkim', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    dmarc(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.dmarc({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/dmarc', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    malicious(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.malicious({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/malicious', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    spam(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.spam({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/spam', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    spf(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.spf({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/spf', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    spoof(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.spoof({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/spoof', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    threatCategory(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.threatCategory({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/threat_category', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    tlsVersion(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.tlsVersion({}, query);
        }
        return this._client.get('/radar/email/security/timeseries_groups/tls_version', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
}
exports.TimeseriesGroups = TimeseriesGroups;
//# sourceMappingURL=timeseries-groups.js.map