// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import { isRequestOptions } from "../../../../core.mjs";
export class TimeseriesGroups extends APIResource {
    bitrate(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.bitrate({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/bitrate', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    duration(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.duration({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/duration', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    industry(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.industry({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/industry', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    ipVersion(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.ipVersion({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/ip_version', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    protocol(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.protocol({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/protocol', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    vector(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.vector({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/vector', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    vertical(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.vertical({}, query);
        }
        return this._client.get('/radar/attacks/layer3/timeseries_groups/vertical', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
}
//# sourceMappingURL=timeseries-groups.mjs.map