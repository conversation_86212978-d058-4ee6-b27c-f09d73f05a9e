/**
 * Content Image Component
 * 
 * Optimized image component for CMS content with automatic optimization,
 * blur placeholders, and responsive sizing.
 */

import Image from 'next/image'
import type { ImageMetadata } from '@/cms/types'

interface ContentImageProps {
  image: ImageMetadata | string
  alt: string
  className?: string
  priority?: boolean
  fill?: boolean
  sizes?: string
  quality?: number
}

/**
 * Content Image Component
 * 
 * Renders optimized images from CMS content with automatic blur placeholders
 * and responsive sizing. Supports both ImageMetadata objects and string URLs.
 */
export function ContentImage({
  image,
  alt,
  className,
  priority = false,
  fill = false,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
  quality = 80
}: ContentImageProps) {
  // Handle string URLs (fallback)
  if (typeof image === 'string') {
    return (
      <Image
        src={image}
        alt={alt}
        className={className}
        priority={priority}
        fill={fill}
        sizes={sizes}
        quality={quality}
      />
    )
  }
  
  // Handle ImageMetadata objects with optimization
  return (
    <Image
      src={image.src}
      alt={alt || image.alt || ''}
      width={fill ? undefined : image.width}
      height={fill ? undefined : image.height}
      placeholder={image.blurDataURL ? 'blur' : 'empty'}
      blurDataURL={image.blurDataURL}
      className={className}
      priority={priority}
      fill={fill}
      sizes={sizes}
      quality={quality}
    />
  )
}

/**
 * Content Cover Image Component
 * 
 * Specialized component for content cover images with consistent styling.
 */
interface ContentCoverImageProps {
  image: ImageMetadata | string
  title: string
  className?: string
  priority?: boolean
}

export function ContentCoverImage({
  image,
  title,
  className = "w-full rounded-lg border object-cover",
  priority = false
}: ContentCoverImageProps) {
  return (
    <ContentImage
      image={image}
      alt={`Cover image for ${title}`}
      className={className}
      priority={priority}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
    />
  )
}

/**
 * Content Thumbnail Image Component
 * 
 * Specialized component for content thumbnails with consistent sizing.
 */
interface ContentThumbnailProps {
  image: ImageMetadata | string
  title: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function ContentThumbnail({
  image,
  title,
  className,
  size = 'md'
}: ContentThumbnailProps) {
  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32'
  }
  
  const defaultClassName = `${sizeClasses[size]} rounded object-cover`
  
  return (
    <ContentImage
      image={image}
      alt={`Thumbnail for ${title}`}
      className={className || defaultClassName}
      sizes={`${size === 'sm' ? '64px' : size === 'md' ? '96px' : '128px'}`}
    />
  )
}

/**
 * Author Image Component
 * 
 * Specialized component for author profile images.
 */
interface AuthorImageProps {
  image: ImageMetadata | string
  author: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function AuthorImage({
  image,
  author,
  className,
  size = 'md'
}: AuthorImageProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  }
  
  const defaultClassName = `${sizeClasses[size]} rounded-full object-cover`
  
  return (
    <ContentImage
      image={image}
      alt={`Profile picture of ${author}`}
      className={className || defaultClassName}
      sizes={`${size === 'sm' ? '32px' : size === 'md' ? '48px' : '64px'}`}
    />
  )
}

/**
 * Responsive Content Image Component
 * 
 * Image component that adapts to different screen sizes with art direction.
 */
interface ResponsiveContentImageProps {
  image: ImageMetadata | string
  alt: string
  className?: string
  priority?: boolean
  aspectRatio?: 'square' | 'video' | 'wide' | 'tall'
}

export function ResponsiveContentImage({
  image,
  alt,
  className,
  priority = false,
  aspectRatio = 'video'
}: ResponsiveContentImageProps) {
  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    wide: 'aspect-[21/9]',
    tall: 'aspect-[3/4]'
  }
  
  const containerClassName = `relative overflow-hidden ${aspectRatioClasses[aspectRatio]} ${className || ''}`
  
  return (
    <div className={containerClassName}>
      <ContentImage
        image={image}
        alt={alt}
        fill
        priority={priority}
        className="object-cover"
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      />
    </div>
  )
}

// Export all components
export {
  ContentCoverImage,
  ContentThumbnail,
  AuthorImage,
  ResponsiveContentImage
}
