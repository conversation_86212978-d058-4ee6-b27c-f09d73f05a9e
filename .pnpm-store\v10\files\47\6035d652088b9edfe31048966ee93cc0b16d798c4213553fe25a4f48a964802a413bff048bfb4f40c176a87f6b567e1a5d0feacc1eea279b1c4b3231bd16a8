import { APIResource } from "../../../../resource.js";
import * as CurrentsAPI from "./currents.js";
import { CurrentGetParams, CurrentGetResponse, Currents } from "./currents.js";
export declare class Aggregates extends APIResource {
    currents: CurrentsAPI.Currents;
}
export declare namespace Aggregates {
    export { Currents as Currents, type CurrentGetResponse as CurrentGetResponse, type CurrentGetParams as CurrentGetParams, };
}
//# sourceMappingURL=aggregates.d.ts.map