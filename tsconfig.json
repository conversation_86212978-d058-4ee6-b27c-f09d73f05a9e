{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/.source": ["./.source/index.ts"], "@/*": ["./src/*"], "@/cms": ["./src/cms"], "@/cms/*": ["./src/cms/*"], ".velite": ["./.velite"]}, "target": "ES2017"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", ".velite/**/*.ts"], "exclude": ["node_modules"]}