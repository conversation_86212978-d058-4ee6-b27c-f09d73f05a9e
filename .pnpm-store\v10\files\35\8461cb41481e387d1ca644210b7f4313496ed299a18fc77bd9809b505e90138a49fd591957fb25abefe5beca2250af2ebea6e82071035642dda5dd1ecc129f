import { APIResource } from "../../../../resource.js";
import * as AccountMappingAPI from "./account-mapping.js";
import { AccountMapping, AccountMappingCreateParams, AccountMappingCreateResponse, AccountMappingGetParams, AccountMappingGetResponse } from "./account-mapping.js";
import * as RulesAPI from "./rules.js";
import { RuleBulkEditParams, RuleBulkEditResponse, RuleCreateParams, RuleCreateResponse, RuleDeleteParams, RuleDeleteResponse, RuleGetParams, RuleGetResponse, RuleListParams, RuleListResponse, RuleListResponsesSinglePage, RuleUpdateParams, RuleUpdateResponse, Rules } from "./rules.js";
export declare class Email extends APIResource {
    accountMapping: AccountMappingAPI.AccountMapping;
    rules: RulesAPI.Rules;
}
export declare namespace Email {
    export { AccountMapping as AccountMapping, type AccountMappingCreateResponse as AccountMappingCreateResponse, type AccountMappingGetResponse as AccountMappingGetResponse, type AccountMappingCreateParams as AccountMappingCreateParams, type AccountMappingGetParams as AccountMappingGetParams, };
    export { Rules as Rules, type RuleCreateResponse as RuleCreateResponse, type RuleUpdateResponse as RuleUpdateResponse, type RuleListResponse as RuleListResponse, type RuleDeleteResponse as RuleDeleteResponse, type RuleBulkEditResponse as RuleBulkEditResponse, type RuleGetResponse as RuleGetResponse, RuleListResponsesSinglePage as RuleListResponsesSinglePage, type RuleCreateParams as RuleCreateParams, type RuleUpdateParams as RuleUpdateParams, type RuleListParams as RuleListParams, type RuleDeleteParams as RuleDeleteParams, type RuleBulkEditParams as RuleBulkEditParams, type RuleGetParams as RuleGetParams, };
}
//# sourceMappingURL=email.d.ts.map