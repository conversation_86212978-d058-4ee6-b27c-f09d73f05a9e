import { APIResource } from "../../../../resource.js";
import * as Core from "../../../../core.js";
import * as ConfigurationsAPI from "./configurations.js";
export declare class CustomCertificate extends APIResource {
    /**
     * Fetches the current Zero Trust certificate configuration.
     *
     * @deprecated
     */
    get(params: CustomCertificateGetParams, options?: Core.RequestOptions): Core.APIPromise<ConfigurationsAPI.CustomCertificateSettings | null>;
}
export interface CustomCertificateGetParams {
    account_id: string;
}
export declare namespace CustomCertificate {
    export { type CustomCertificateGetParams as CustomCertificateGetParams };
}
//# sourceMappingURL=custom-certificate.d.ts.map