// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import * as ContentListsAPI from "./content-lists/content-lists.mjs";
import { ContentLists, } from "./content-lists/content-lists.mjs";
export class IPFSUniversalPaths extends APIResource {
    constructor() {
        super(...arguments);
        this.contentLists = new ContentListsAPI.ContentLists(this._client);
    }
}
IPFSUniversalPaths.ContentLists = ContentLists;
//# sourceMappingURL=ipfs-universal-paths.mjs.map