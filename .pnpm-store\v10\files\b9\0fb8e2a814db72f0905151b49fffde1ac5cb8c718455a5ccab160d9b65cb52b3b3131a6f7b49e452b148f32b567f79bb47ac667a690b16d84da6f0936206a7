{"version": 3, "file": "cas.mjs", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/access/applications/cas.ts"], "names": [], "mappings": "AAAA,sFAAsF;OAE/E,EAAE,WAAW,EAAE;OACf,EAAE,gBAAgB,EAAE;OAEpB,EAAE,eAAe,EAAE;OACnB,EAAE,UAAU,EAAE;AAErB,MAAM,OAAO,GAAI,SAAQ,WAAW;IAelC,MAAM,CACJ,KAAa,EACb,SAA+C,EAAE,EACjD,OAA6B;QAE7B,IAAI,gBAAgB,CAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SACvC;QACD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QACvC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,eAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,eAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OACE,IAAI,CAAC,OAAO,CAAC,IAAI,CACf,IAAI,aAAa,IAAI,eAAe,gBAAgB,KAAK,KAAK,EAC9D,OAAO,CAEV,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAiBD,IAAI,CACF,SAA6C,EAAE,EAC/C,OAA6B;QAE7B,IAAI,gBAAgB,CAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;SAC9B;QACD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QACvC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,eAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,eAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAC5B,IAAI,aAAa,IAAI,eAAe,iBAAiB,EACrD,aAAa,EACb,OAAO,CACR,CAAC;IACJ,CAAC;IAoBD,MAAM,CACJ,KAAa,EACb,SAA+C,EAAE,EACjD,OAA6B;QAE7B,IAAI,gBAAgB,CAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SACvC;QACD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QACvC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,eAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,eAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OACE,IAAI,CAAC,OAAO,CAAC,MAAM,CACjB,IAAI,aAAa,IAAI,eAAe,gBAAgB,KAAK,KAAK,EAC9D,OAAO,CAEV,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAgBD,GAAG,CACD,KAAa,EACb,SAA4C,EAAE,EAC9C,OAA6B;QAE7B,IAAI,gBAAgB,CAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SACpC;QACD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QACvC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,eAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,eAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OACE,IAAI,CAAC,OAAO,CAAC,GAAG,CACd,IAAI,aAAa,IAAI,eAAe,gBAAgB,KAAK,KAAK,EAC9D,OAAO,CAEV,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;CACF;AAED,MAAM,OAAO,aAAc,SAAQ,UAAc;CAAG;AA2EpD,GAAG,CAAC,aAAa,GAAG,aAAa,CAAC"}