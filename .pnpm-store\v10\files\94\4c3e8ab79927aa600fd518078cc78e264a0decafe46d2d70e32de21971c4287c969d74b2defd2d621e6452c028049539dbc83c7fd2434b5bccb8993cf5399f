// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import { isRequestOptions } from "../../../../core.mjs";
export class TimeseriesGroups extends APIResource {
    httpMethod(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.httpMethod({}, query);
        }
        return this._client.get('/radar/attacks/layer7/timeseries_groups/http_method', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    httpVersion(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.httpVersion({}, query);
        }
        return this._client.get('/radar/attacks/layer7/timeseries_groups/http_version', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    industry(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.industry({}, query);
        }
        return this._client.get('/radar/attacks/layer7/timeseries_groups/industry', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    ipVersion(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.ipVersion({}, query);
        }
        return this._client.get('/radar/attacks/layer7/timeseries_groups/ip_version', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    managedRules(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.managedRules({}, query);
        }
        return this._client.get('/radar/attacks/layer7/timeseries_groups/managed_rules', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    mitigationProduct(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.mitigationProduct({}, query);
        }
        return this._client.get('/radar/attacks/layer7/timeseries_groups/mitigation_product', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
    vertical(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.vertical({}, query);
        }
        return this._client.get('/radar/attacks/layer7/timeseries_groups/vertical', {
            query,
            ...options,
        })._thenUnwrap((obj) => obj.result);
    }
}
//# sourceMappingURL=timeseries-groups.mjs.map