/**
 * Main CMS Class
 * 
 * Central orchestrator for the CMS system. Manages provider initialization,
 * service creation, and provides a high-level API for content operations.
 */

import { providerFactory, providerRegistry } from './providers'
import { ServiceFactory } from './services/factory'
import type {
  CMSProvider,
  ContentItem,
  ContentType,
  ContentQueryOptions,
  ContentAvailability
} from './types'

interface CMSConfig {
  provider: {
    name: string
    config?: any
  }
  baseUrl: string
  siteName: string
  siteDescription: string
  defaultLocale: string
  locales: string[]
  cache: {
    enabled: boolean
    defaultTTL: number
    maxSize: number
  }
  social: {
    twitter?: string
    facebook?: string
  }
}

export class CMS {
  private static instance: CMS
  private provider?: CMSProvider
  private serviceFactory?: ServiceFactory
  private config?: CMSConfig
  
  private constructor() {}
  
  /**
   * Get singleton instance
   */
  static getInstance(): CMS {
    if (!CMS.instance) {
      CMS.instance = new CMS()
    }
    return CMS.instance
  }
  
  /**
   * Initialize CMS with configuration
   */
  async initialize(config: CMSConfig): Promise<void> {
    this.config = config
    
    // Create provider instance
    this.provider = providerFactory.create(
      config.provider.name,
      config.provider.config
    )
    
    // Register and activate provider
    providerRegistry.register(this.provider)
    providerRegistry.setActive(this.provider.name)
    
    // Initialize provider
    await this.provider.initialize?.()
    
    // Create service factory
    this.serviceFactory = new ServiceFactory({
      baseUrl: config.baseUrl,
      siteName: config.siteName,
      siteDescription: config.siteDescription,
      defaultLocale: config.defaultLocale,
      locales: config.locales,
      cache: config.cache,
      social: config.social
    })
    
    console.log(`CMS initialized with provider: ${this.provider.name}`)
  }
  
  /**
   * Get content service
   */
  getContentService() {
    this.ensureInitialized()
    return this.serviceFactory!.getContentService(this.provider!)
  }
  
  /**
   * Get cache service
   */
  getCacheService() {
    this.ensureInitialized()
    return this.serviceFactory!.getCacheService()
  }
  
  /**
   * Get SEO service
   */
  getSEOService() {
    this.ensureInitialized()
    return this.serviceFactory!.getSEOService()
  }
  
  /**
   * Convenience methods for common operations
   */
  
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    return this.getContentService().getContent<T>(type, slug, locale)
  }
  
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: ContentQueryOptions
  ): Promise<T[]> {
    return this.getContentService().getContentList<T>(type, locale, options)
  }
  
  async contentExists(type: ContentType, slug: string, locale: string): Promise<boolean> {
    return this.getContentService().contentExists(type, slug, locale)
  }
  
  async getContentTitle(type: ContentType, slug: string, locale: string): Promise<string | null> {
    return this.getContentService().getContentTitle(type, slug, locale)
  }
  
  async getAvailableLanguageVersions(
    type: ContentType,
    slug: string
  ): Promise<ContentAvailability[]> {
    return this.getContentService().getAvailableLanguageVersions(type, slug)
  }
  
  async getRelatedContent<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit?: number
  ): Promise<T[]> {
    return this.getContentService().getRelatedContent<T>(type, currentSlug, locale, limit)
  }
  
  async getFeaturedContent<T extends ContentItem>(
    type: ContentType,
    locale: string,
    limit?: number
  ): Promise<T[]> {
    return this.getContentService().getFeaturedContent<T>(type, locale, limit)
  }
  
  async getRecentContent<T extends ContentItem>(
    type: ContentType,
    locale: string,
    limit?: number
  ): Promise<T[]> {
    return this.getContentService().getRecentContent<T>(type, locale, limit)
  }
  
  /**
   * Get current provider info
   */
  getProviderInfo(): { name: string; version: string } | null {
    if (!this.provider) return null
    return {
      name: this.provider.name,
      version: this.provider.version
    }
  }
  
  /**
   * Get available providers
   */
  getAvailableProviders(): string[] {
    return providerFactory.getAvailableProviders()
  }
  
  /**
   * Switch to a different provider
   */
  async switchProvider(name: string, config?: any): Promise<void> {
    if (!this.config) {
      throw new Error('CMS not initialized')
    }
    
    // Destroy current provider
    await this.provider?.destroy?.()
    
    // Create new provider
    this.provider = providerFactory.create(name, config)
    
    // Register and activate new provider
    providerRegistry.register(this.provider)
    providerRegistry.setActive(this.provider.name)
    
    // Initialize new provider
    await this.provider.initialize?.()
    
    // Reset service factory to use new provider
    this.serviceFactory?.reset()
    
    console.log(`Switched to provider: ${this.provider.name}`)
  }
  
  /**
   * Cleanup resources
   */
  async destroy(): Promise<void> {
    await this.provider?.destroy?.()
    this.provider = undefined
    this.serviceFactory = undefined
    this.config = undefined
  }
  
  /**
   * Ensure CMS is initialized
   */
  private ensureInitialized(): void {
    if (!this.provider || !this.serviceFactory) {
      throw new Error('CMS not initialized. Call initialize() first.')
    }
  }
}

// Export singleton instance
export const cms = CMS.getInstance()

// Initialize CMS with default configuration
export async function initializeCMS() {
  const { cmsConfig, serviceConfig } = await import('./config')
  await cms.initialize({
    provider: cmsConfig.provider,
    ...serviceConfig
  })
}
