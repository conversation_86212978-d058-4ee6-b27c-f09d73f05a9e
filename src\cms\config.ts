/**
 * CMS Configuration
 * 
 * Central configuration for the CMS system.
 * This file defines the active provider and system settings.
 */

import type { CMSConfig } from './types'

// Get base URL from environment or default
const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://shipany.ai'

export const cmsConfig: CMSConfig = {
  // Active CMS provider configuration
  provider: {
    name: 'velite',
    config: {
      // Velite-specific configuration
      root: './content',
      output: {
        data: '.velite',
        assets: 'public/static',
        base: '/static/',
        name: '[name]-[hash:8].[ext]',
        clean: true
      },
      cache: {
        enabled: true,
        dir: '.velite/cache'
      },
      concurrency: Math.max(1, Math.floor(require('os').cpus().length / 2))
    }
  },
  
  // Content type configurations
  contentTypes: {
    blog: {
      fields: ['title', 'description', 'content', 'tags', 'publishedAt', 'author'],
      required: ['title', 'description', 'content'],
      multilingual: true,
      seo: {
        enabled: true,
        structuredDataType: 'BlogPosting'
      }
    },
    product: {
      fields: ['title', 'description', 'content', 'price', 'category'],
      required: ['title', 'description', 'content'],
      multilingual: true,
      seo: {
        enabled: true,
        structuredDataType: 'SoftwareApplication'
      }
    },
    'case-study': {
      fields: ['title', 'description', 'content', 'client', 'industry'],
      required: ['title', 'description', 'content'],
      multilingual: true,
      seo: {
        enabled: true,
        structuredDataType: 'Article'
      }
    }
  },
  
  // Cache configuration
  cache: {
    enabled: true,
    ttl: 5 * 60 * 1000, // 5 minutes
    strategy: 'memory'
  },
  
  // SEO configuration
  seo: {
    sitemap: {
      enabled: true,
      filename: 'sitemap.xml',
      changefreq: 'weekly',
      priority: {
        default: 0.7,
        featured: 0.9,
        static: 0.8
      },
      multilingual: {
        enabled: true,
        hreflang: true
      }
    },
    rss: {
      enabled: true,
      limit: 20,
      multilingual: true,
      feeds: {
        en: {
          filename: 'rss.xml',
          title: 'ShipAny Blog',
          description: 'Build any AI SaaS startup in hours',
          language: 'en-US',
          contentTypes: ['blog']
        },
        zh: {
          filename: 'rss-zh.xml',
          title: 'ShipAny 博客',
          description: '几小时内构建任何AI SaaS创业项目',
          language: 'zh-CN',
          contentTypes: ['blog']
        }
      }
    },
    structuredData: {
      enabled: true,
      types: {
        blog: true,
        product: true,
        caseStudy: true,
        organization: true,
        breadcrumb: true
      }
    }
  },
  
  // Development configuration
  development: {
    watch: process.env.NODE_ENV === 'development',
    hotReload: process.env.NODE_ENV === 'development',
    validation: true
  }
}

// Service factory configuration
export const serviceConfig = {
  baseUrl,
  siteName: 'ShipAny',
  siteDescription: 'Build any AI SaaS startup in hours',
  defaultLocale: 'en',
  locales: ['en', 'zh'],
  cache: {
    enabled: cmsConfig.cache.enabled,
    defaultTTL: cmsConfig.cache.ttl,
    maxSize: 1000
  },
  social: {
    twitter: '@shipany_ai',
    // Add other social media handles as needed
  }
}
