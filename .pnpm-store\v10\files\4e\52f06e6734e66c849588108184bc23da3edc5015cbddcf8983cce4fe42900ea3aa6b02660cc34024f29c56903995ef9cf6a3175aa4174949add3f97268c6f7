import { APIResource } from "../../../../resource.js";
import * as EventsAPI from "./events.js";
import { EventListParams, EventListResponse, EventListResponsesV4PagePagination, Events } from "./events.js";
export declare class Hijacks extends APIResource {
    events: EventsAPI.Events;
}
export declare namespace Hijacks {
    export { Events as Events, type EventListResponse as EventListResponse, EventListResponsesV4PagePagination as EventListResponsesV4PagePagination, type EventListParams as EventListParams, };
}
//# sourceMappingURL=hijacks.d.ts.map