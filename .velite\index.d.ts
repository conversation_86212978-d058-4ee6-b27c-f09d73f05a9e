// This file is generated by Velite

import type __vc from '../velite.config.ts'

type Collections = typeof __vc.collections

export type Blog = Collections['blogs']['schema']['_output']
export declare const blogs: Blog[]

export type Product = Collections['products']['schema']['_output']
export declare const products: Product[]

export type CaseStudy = Collections['caseStudies']['schema']['_output']
export declare const caseStudies: CaseStudy[]
