import { APIResource } from "../../../../resource.js";
import * as SummaryAPI from "./summary.js";
import { Summary, SummaryARCParams, SummaryARCResponse, SummaryDKIMParams, SummaryDKIMResponse, SummaryDMARCParams, SummaryDMARCResponse, SummaryEncryptedParams, SummaryEncryptedResponse, SummaryIPVersionParams, SummaryIPVersionResponse, SummarySPFParams, SummarySPFResponse } from "./summary.js";
import * as TimeseriesGroupsAPI from "./timeseries-groups.js";
import { TimeseriesGroupARCParams, TimeseriesGroupARCResponse, TimeseriesGroupDKIMParams, TimeseriesGroupDKIMResponse, TimeseriesGroupDMARCParams, TimeseriesGroupDMARCResponse, TimeseriesGroupEncryptedParams, TimeseriesGroupEncryptedResponse, TimeseriesGroupIPVersionParams, TimeseriesGroupIPVersionResponse, TimeseriesGroupSPFParams, TimeseriesGroupSPFResponse, TimeseriesGroups } from "./timeseries-groups.js";
export declare class Routing extends APIResource {
    summary: SummaryAPI.Summary;
    timeseriesGroups: TimeseriesGroupsAPI.TimeseriesGroups;
}
export declare namespace Routing {
    export { Summary as Summary, type SummaryARCResponse as SummaryARCResponse, type SummaryDKIMResponse as SummaryDKIMResponse, type SummaryDMARCResponse as SummaryDMARCResponse, type SummaryEncryptedResponse as SummaryEncryptedResponse, type SummaryIPVersionResponse as SummaryIPVersionResponse, type SummarySPFResponse as SummarySPFResponse, type SummaryARCParams as SummaryARCParams, type SummaryDKIMParams as SummaryDKIMParams, type SummaryDMARCParams as SummaryDMARCParams, type SummaryEncryptedParams as SummaryEncryptedParams, type SummaryIPVersionParams as SummaryIPVersionParams, type SummarySPFParams as SummarySPFParams, };
    export { TimeseriesGroups as TimeseriesGroups, type TimeseriesGroupARCResponse as TimeseriesGroupARCResponse, type TimeseriesGroupDKIMResponse as TimeseriesGroupDKIMResponse, type TimeseriesGroupDMARCResponse as TimeseriesGroupDMARCResponse, type TimeseriesGroupEncryptedResponse as TimeseriesGroupEncryptedResponse, type TimeseriesGroupIPVersionResponse as TimeseriesGroupIPVersionResponse, type TimeseriesGroupSPFResponse as TimeseriesGroupSPFResponse, type TimeseriesGroupARCParams as TimeseriesGroupARCParams, type TimeseriesGroupDKIMParams as TimeseriesGroupDKIMParams, type TimeseriesGroupDMARCParams as TimeseriesGroupDMARCParams, type TimeseriesGroupEncryptedParams as TimeseriesGroupEncryptedParams, type TimeseriesGroupIPVersionParams as TimeseriesGroupIPVersionParams, type TimeseriesGroupSPFParams as TimeseriesGroupSPFParams, };
}
//# sourceMappingURL=routing.d.ts.map