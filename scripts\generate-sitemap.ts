/**
 * Enhanced Sitemap Generator
 * 
 * Generates a comprehensive sitemap.xml with multi-language support,
 * hreflang attributes, and proper SEO optimization using the new CMS system.
 */

import { writeFileSync } from 'fs'
import { cms, initializeCMS } from '../src/cms'
import type { ContentItem } from '../src/cms/types'

// Get base URL from environment or default
const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://shipany.ai'
const locales = ['en', 'zh']

interface SitemapEntry {
  url: string
  lastmod?: string
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: number
  alternates?: Array<{ hreflang: string; href: string }>
}

async function generateSitemap() {
  console.log('🗺️  Generating enhanced sitemap...')
  
  // Initialize CMS
  await initializeCMS()
  
  const entries: SitemapEntry[] = []
  
  // Static pages with language alternates
  const staticPages = [
    { path: '', priority: 1.0, changefreq: 'daily' as const },
    { path: '/pricing', priority: 0.8, changefreq: 'weekly' as const },
    { path: '/showcase', priority: 0.8, changefreq: 'weekly' as const },
    { path: '/blogs', priority: 0.9, changefreq: 'daily' as const },
    { path: '/products', priority: 0.9, changefreq: 'weekly' as const },
    { path: '/case-studies', priority: 0.8, changefreq: 'weekly' as const },
  ]
  
  // Add static pages with alternates
  staticPages.forEach(page => {
    locales.forEach(locale => {
      const url = locale === 'en' 
        ? `${baseUrl}${page.path}` 
        : `${baseUrl}/${locale}${page.path}`
      
      const alternates = locales.map(altLocale => ({
        hreflang: altLocale === 'zh' ? 'zh-CN' : 'en-US',
        href: altLocale === 'en' 
          ? `${baseUrl}${page.path}` 
          : `${baseUrl}/${altLocale}${page.path}`
      }))
      
      entries.push({
        url,
        changefreq: page.changefreq,
        priority: page.priority,
        alternates
      })
    })
  })
  
  // Get content in all languages
  const allContent: ContentItem[] = []
  for (const locale of locales) {
    try {
      const [localBlogs, localProducts, localCaseStudies] = await Promise.all([
        cms.getContentList('blog', locale),
        cms.getContentList('product', locale),
        cms.getContentList('case-study', locale)
      ])
      allContent.push(...localBlogs, ...localProducts, ...localCaseStudies)
    } catch (error) {
      console.warn(`Failed to get content for locale ${locale}:`, error)
    }
  }
  
  // Group content by slug to create alternates
  const contentBySlug = new Map<string, ContentItem[]>()
  allContent.forEach(content => {
    if (!contentBySlug.has(content.slug)) {
      contentBySlug.set(content.slug, [])
    }
    contentBySlug.get(content.slug)!.push(content)
  })
  
  // Add content pages with alternates
  contentBySlug.forEach((versions, slug) => {
    versions.forEach(content => {
      const alternates = versions
        .map(version => ({
          hreflang: version.lang === 'zh' ? 'zh-CN' : 'en-US',
          href: `${baseUrl}${version.url}`
        }))
        .filter((alt, index, arr) => 
          arr.findIndex(a => a.hreflang === alt.hreflang) === index
        )
      
      entries.push({
        url: `${baseUrl}${content.url}`,
        lastmod: content.updatedAt || content.publishedAt || content.createdAt,
        changefreq: 'weekly',
        priority: content.featured ? 0.9 : 0.7,
        alternates: alternates.length > 1 ? alternates : undefined
      })
    })
  })
  
  // Generate XML
  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" 
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
`
  
  // Remove duplicates and sort by priority
  const uniqueEntries = entries.filter((entry, index, arr) => 
    arr.findIndex(e => e.url === entry.url) === index
  ).sort((a, b) => b.priority - a.priority)
  
  uniqueEntries.forEach(entry => {
    sitemap += `  <url>
    <loc>${entry.url}</loc>
`
    
    if (entry.lastmod) {
      const date = new Date(entry.lastmod).toISOString().split('T')[0]
      sitemap += `    <lastmod>${date}</lastmod>
`
    }
    
    sitemap += `    <changefreq>${entry.changefreq}</changefreq>
    <priority>${entry.priority}</priority>
`
    
    if (entry.alternates) {
      entry.alternates.forEach(alt => {
        sitemap += `    <xhtml:link rel="alternate" hreflang="${alt.hreflang}" href="${alt.href}" />
`
      })
    }
    
    sitemap += `  </url>
`
  })
  
  sitemap += `</urlset>`
  
  // Write sitemap
  writeFileSync('public/sitemap.xml', sitemap)
  
  console.log(`✅ Enhanced sitemap generated with ${uniqueEntries.length} URLs`)
  console.log(`   - Static pages: ${staticPages.length * locales.length}`)
  console.log(`   - Content pages: ${uniqueEntries.length - (staticPages.length * locales.length)}`)
  console.log(`   - Multi-language support: ${locales.join(', ')}`)
}

// Run the generator
generateSitemap().catch(console.error)
