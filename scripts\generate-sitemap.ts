/**
 * Sitemap Generation Script
 *
 * This script generates a comprehensive sitemap.xml file that includes:
 * - Static pages (home, pricing, showcase)
 * - MDX content pages (blogs, products, case studies)
 * - Multi-language support with hreflang attributes
 * - Proper priority and change frequency settings
 */

import { writeFileSync } from 'fs'
import { allBlogs, allProducts, allCaseStudies } from '../.content-collections/generated/index.js'

const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'https://shipany.ai'
const locales = ['en', 'zh']

/**
 * Main sitemap generation function
 *
 * Generates a complete sitemap with all static and dynamic pages,
 * including proper XML structure and SEO attributes.
 */
function generateSitemap() {
  // Define static pages that exist for all locales
  const staticPages = [
    '',           // Home page
    '/pricing',   // Pricing page
    '/showcase',  // Showcase page
  ]

  // Initialize sitemap XML with proper namespaces
  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">
`

  // Add static pages for each locale
  staticPages.forEach(page => {
    locales.forEach(locale => {
      // Generate URL with proper locale handling (English has no prefix)
      const url = locale === 'en' ? `${baseUrl}${page}` : `${baseUrl}/${locale}${page}`
      sitemap += `  <url>
    <loc>${url}</loc>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
`
      // Add hreflang attributes for all language versions
      locales.forEach(altLocale => {
        const altUrl = altLocale === 'en' ? `${baseUrl}${page}` : `${baseUrl}/${altLocale}${page}`
        sitemap += `    <xhtml:link rel="alternate" hreflang="${altLocale}" href="${altUrl}" />
`
      })
      sitemap += `  </url>
`
    })
  })

  // Add MDX blog posts
  allBlogs.forEach(blog => {
    // Generate URL with proper locale handling
    const url = blog.lang === 'en' ? `${baseUrl}/blogs/${blog.slug}` : `${baseUrl}/${blog.lang}/blogs/${blog.slug}`
    sitemap += `  <url>
    <loc>${url}</loc>
    <lastmod>${blog.publishedAt || blog.createdAt}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>
`
  })

  // Add MDX product pages
  allProducts.forEach(product => {
    // Generate URL with proper locale handling
    const url = product.lang === 'en' ? `${baseUrl}/products/${product.slug}` : `${baseUrl}/${product.lang}/products/${product.slug}`
    sitemap += `  <url>
    <loc>${url}</loc>
    <lastmod>${product.publishedAt || product.createdAt}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
`
  })

  // Add MDX case studies
  allCaseStudies.forEach(caseStudy => {
    // Generate URL with proper locale handling
    const url = caseStudy.lang === 'en' ? `${baseUrl}/case-studies/${caseStudy.slug}` : `${baseUrl}/${caseStudy.lang}/case-studies/${caseStudy.slug}`
    sitemap += `  <url>
    <loc>${url}</loc>
    <lastmod>${caseStudy.publishedAt || caseStudy.createdAt}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
`
  })

  // Add content index pages (blogs, products, case-studies)
  const contentPages = ['/blogs', '/products', '/case-studies']
  contentPages.forEach(page => {
    locales.forEach(locale => {
      // Generate URL with proper locale handling
      const url = locale === 'en' ? `${baseUrl}${page}` : `${baseUrl}/${locale}${page}`
      sitemap += `  <url>
    <loc>${url}</loc>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
`
    })
  })

  // Close the sitemap XML
  sitemap += `</urlset>`

  // Write sitemap to public directory
  writeFileSync('public/sitemap.xml', sitemap)
  console.log('✅ Sitemap generated successfully!')
}

// Execute the sitemap generation
generateSitemap()
