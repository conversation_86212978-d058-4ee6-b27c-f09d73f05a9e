/**
 * CMS Service Tests
 * 
 * Test suite for the main CMS service that coordinates between different
 * providers and provides a unified interface for content operations.
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { CMSService } from '../services'
import type { CMSConfig, ContentItem } from '../types'

// Mock the Contentlayer2Provider
jest.mock('../providers/contentlayer2/provider', () => ({
  Contentlayer2Provider: jest.fn().mockImplementation(() => ({
    name: 'contentlayer2',
    version: '0.4.6',
    getContent: jest.fn(),
    getContentList: jest.fn(),
    contentExists: jest.fn(),
    getContentTitle: jest.fn(),
    getContentMetadata: jest.fn(),
    getAvailableLanguages: jest.fn(),
    getRelatedContent: jest.fn()
  }))
}))

describe('CMSService', () => {
  let cmsService: CMSService
  let mockProvider: any

  const defaultConfig: CMSConfig = {
    provider: 'contentlayer2',
    contentTypes: ['blog', 'product', 'case-study'],
    defaultLocale: 'en',
    supportedLocales: ['en', 'zh'],
    features: {
      cache: true,
      seo: true,
      relatedContent: true,
      languageSwitching: true
    }
  }

  beforeEach(async () => {
    cmsService = new CMSService()
    await cmsService.initialize(defaultConfig)
    
    // Get reference to the mocked provider
    const { Contentlayer2Provider } = await import('../providers/contentlayer2/provider')
    mockProvider = (Contentlayer2Provider as jest.MockedClass<any>).mock.instances[0]
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('Initialization', () => {
    it('should initialize with contentlayer2 provider', async () => {
      const service = new CMSService()
      await service.initialize(defaultConfig)
      
      expect(service.isInitialized()).toBe(true)
      expect(service.getProviderInfo()?.name).toBe('contentlayer2')
      expect(service.getProviderInfo()?.version).toBe('0.4.6')
    })

    it('should store configuration correctly', async () => {
      const service = new CMSService()
      await service.initialize(defaultConfig)
      
      const config = service.getConfig()
      expect(config).toEqual(defaultConfig)
    })

    it('should throw error for unknown provider', async () => {
      const service = new CMSService()
      const invalidConfig = { ...defaultConfig, provider: 'unknown' as any }
      
      await expect(service.initialize(invalidConfig)).rejects.toThrow('Unknown CMS provider: unknown')
    })

    it('should throw error for unimplemented providers', async () => {
      const service = new CMSService()
      
      await expect(service.initialize({ ...defaultConfig, provider: 'strapi' }))
        .rejects.toThrow('Strapi provider not yet implemented')
      
      await expect(service.initialize({ ...defaultConfig, provider: 'sanity' }))
        .rejects.toThrow('Sanity provider not yet implemented')
      
      await expect(service.initialize({ ...defaultConfig, provider: 'contentful' }))
        .rejects.toThrow('Contentful provider not yet implemented')
      
      await expect(service.initialize({ ...defaultConfig, provider: 'nextjs-mdx' }))
        .rejects.toThrow('Next.js MDX provider not yet implemented')
    })
  })

  describe('Content Operations', () => {
    const mockContent: ContentItem = {
      slug: 'test-content',
      title: 'Test Content',
      lang: 'en',
      url: '/test/en/test-content',
      description: 'Test description',
      body: { raw: 'Test content', html: '<p>Test content</p>' },
      createdAt: '2024-01-01T00:00:00Z',
      featured: false
    }

    describe('getContent', () => {
      it('should delegate to provider and return content', async () => {
        mockProvider.getContent.mockResolvedValue(mockContent)
        
        const result = await cmsService.getContent('blog', 'test-content', 'en')
        
        expect(mockProvider.getContent).toHaveBeenCalledWith('blog', 'test-content', 'en')
        expect(result).toEqual(mockContent)
      })

      it('should return null when content not found', async () => {
        mockProvider.getContent.mockResolvedValue(null)
        
        const result = await cmsService.getContent('blog', 'non-existent', 'en')
        
        expect(result).toBeNull()
      })

      it('should handle provider errors gracefully', async () => {
        mockProvider.getContent.mockRejectedValue(new Error('Provider error'))
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
        
        const result = await cmsService.getContent('blog', 'test-content', 'en')
        
        expect(result).toBeNull()
        expect(consoleSpy).toHaveBeenCalled()
        
        consoleSpy.mockRestore()
      })

      it('should throw error when not initialized', async () => {
        const uninitializedService = new CMSService()
        
        expect(() => uninitializedService.getContent('blog', 'test', 'en'))
          .rejects.toThrow('CMS service not initialized')
      })
    })

    describe('getContentList', () => {
      it('should delegate to provider and return content list', async () => {
        const mockContentList = [mockContent]
        mockProvider.getContentList.mockResolvedValue(mockContentList)
        
        const result = await cmsService.getContentList('blog', 'en', { limit: 10 })
        
        expect(mockProvider.getContentList).toHaveBeenCalledWith('blog', 'en', { limit: 10 })
        expect(result).toEqual(mockContentList)
      })

      it('should handle provider errors gracefully', async () => {
        mockProvider.getContentList.mockRejectedValue(new Error('Provider error'))
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
        
        const result = await cmsService.getContentList('blog', 'en')
        
        expect(result).toEqual([])
        expect(consoleSpy).toHaveBeenCalled()
        
        consoleSpy.mockRestore()
      })
    })

    describe('contentExists', () => {
      it('should delegate to provider', async () => {
        mockProvider.contentExists.mockResolvedValue(true)
        
        const result = await cmsService.contentExists('blog', 'test-content', 'en')
        
        expect(mockProvider.contentExists).toHaveBeenCalledWith('blog', 'test-content', 'en')
        expect(result).toBe(true)
      })

      it('should handle provider errors gracefully', async () => {
        mockProvider.contentExists.mockRejectedValue(new Error('Provider error'))
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
        
        const result = await cmsService.contentExists('blog', 'test-content', 'en')
        
        expect(result).toBe(false)
        expect(consoleSpy).toHaveBeenCalled()
        
        consoleSpy.mockRestore()
      })
    })

    describe('getContentTitle', () => {
      it('should delegate to provider', async () => {
        mockProvider.getContentTitle.mockResolvedValue('Test Title')
        
        const result = await cmsService.getContentTitle('blog', 'test-content', 'en')
        
        expect(mockProvider.getContentTitle).toHaveBeenCalledWith('blog', 'test-content', 'en')
        expect(result).toBe('Test Title')
      })
    })

    describe('getContentMetadata', () => {
      it('should delegate to provider', async () => {
        const mockMetadata = {
          wordCount: 100,
          readingTime: 1,
          publishedAt: '2024-01-01',
          updatedAt: '2024-01-01',
          author: 'Test Author',
          tags: ['test']
        }
        mockProvider.getContentMetadata.mockResolvedValue(mockMetadata)
        
        const result = await cmsService.getContentMetadata('blog', 'test-content', 'en')
        
        expect(mockProvider.getContentMetadata).toHaveBeenCalledWith('blog', 'test-content', 'en')
        expect(result).toEqual(mockMetadata)
      })
    })

    describe('getAvailableLanguages', () => {
      it('should delegate to provider', async () => {
        const mockLanguages = [
          { lang: 'en', title: 'English Title', url: '/en/test', available: true },
          { lang: 'zh', title: '中文标题', url: '/zh/test', available: true }
        ]
        mockProvider.getAvailableLanguages.mockResolvedValue(mockLanguages)
        
        const result = await cmsService.getAvailableLanguages('blog', 'test-content')
        
        expect(mockProvider.getAvailableLanguages).toHaveBeenCalledWith('blog', 'test-content')
        expect(result).toEqual(mockLanguages)
      })
    })

    describe('getRelatedContent', () => {
      it('should delegate to provider when available', async () => {
        const mockRelated = [mockContent]
        mockProvider.getRelatedContent.mockResolvedValue(mockRelated)
        
        const result = await cmsService.getRelatedContent('blog', 'current-post', 'en', 3)
        
        expect(mockProvider.getRelatedContent).toHaveBeenCalledWith('blog', 'current-post', 'en', 3)
        expect(result).toEqual(mockRelated)
      })

      it('should fallback to recent content when provider does not support related content', async () => {
        // Remove getRelatedContent method from mock provider
        delete mockProvider.getRelatedContent
        
        const mockRecentContent = [mockContent, { ...mockContent, slug: 'current-post' }]
        mockProvider.getContentList.mockResolvedValue(mockRecentContent)
        
        const result = await cmsService.getRelatedContent('blog', 'current-post', 'en', 3)
        
        expect(mockProvider.getContentList).toHaveBeenCalledWith('blog', 'en', {
          sortBy: 'publishedAt',
          order: 'desc',
          limit: 4 // limit + 1 to account for filtering
        })
        expect(result).toHaveLength(1) // Current post filtered out
        expect(result[0].slug).toBe('test-content')
      })

      it('should handle provider errors gracefully', async () => {
        mockProvider.getRelatedContent.mockRejectedValue(new Error('Provider error'))
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
        
        const result = await cmsService.getRelatedContent('blog', 'current-post', 'en', 3)
        
        expect(result).toEqual([])
        expect(consoleSpy).toHaveBeenCalled()
        
        consoleSpy.mockRestore()
      })
    })
  })

  describe('Service State', () => {
    it('should report initialization status correctly', () => {
      expect(cmsService.isInitialized()).toBe(true)
      
      const uninitializedService = new CMSService()
      expect(uninitializedService.isInitialized()).toBe(false)
    })

    it('should return provider info when initialized', () => {
      const info = cmsService.getProviderInfo()
      expect(info).toEqual({
        name: 'contentlayer2',
        version: '0.4.6'
      })
    })

    it('should return null provider info when not initialized', () => {
      const uninitializedService = new CMSService()
      expect(uninitializedService.getProviderInfo()).toBeNull()
    })

    it('should return config when initialized', () => {
      const config = cmsService.getConfig()
      expect(config).toEqual(defaultConfig)
    })

    it('should return null config when not initialized', () => {
      const uninitializedService = new CMSService()
      expect(uninitializedService.getConfig()).toBeNull()
    })
  })
})
