/**
 * Content Queries Service
 * 
 * This service handles all content-related data queries, including checking
 * content existence and retrieving content metadata. It serves as the primary
 * interface between the application and the content data source.
 * 
 * Key Features:
 * - Content existence checking across all supported languages
 * - Content title retrieval for display purposes
 * - Type-safe content access with error handling
 * 
 * FUTURE EXTENSIBILITY - Headless CMS Integration:
 * This service is the primary candidate for the adapter pattern. When migrating
 * to a Headless CMS, these functions would be replaced with API calls while
 * maintaining the same interface contracts.
 * 
 * Migration Strategy:
 * - Replace direct Contentlayer imports with adapter calls
 * - Add caching layer for API responses
 * - Implement error handling for network requests
 * - Maintain the same function signatures for backward compatibility
 */

import type { ContentType } from './types'
import { cms } from '@/cms'

// CMS Integration - Using the new abstraction layer
//
// This now uses the CMS abstraction layer which can work with any provider
// (<PERSON>eli<PERSON>, Strapi, Sanity, Contentful, etc.) through a unified interface.
// The implementation automatically handles provider-specific details while
// maintaining the same API for consuming code.

/**
 * Check if content exists in a specific language
 *
 * This function uses the CMS abstraction layer to determine if a specific
 * piece of content (identified by type and slug) has a translation available in
 * the target language. It's used to determine language availability before
 * attempting navigation.
 *
 * The function now works with any CMS provider through the unified interface,
 * automatically handling provider-specific details and caching.
 *
 * @param contentType - Type of content to check ('blog', 'product', 'case-study')
 * @param slug - Unique identifier for the content
 * @param locale - Target locale to check (e.g., 'en', 'zh')
 * @returns true if content exists in the target locale, false otherwise
 */
export async function contentExistsInLocale(
  contentType: ContentType,
  slug: string,
  locale: string
): Promise<boolean> {
  // Early return for non-content types or missing slugs
  if (contentType === 'other' || !slug) {
    return false
  }

  try {
    // Use the CMS abstraction layer to check content existence
    return await cms.contentExists(contentType, slug, locale)
  } catch (error) {
    console.error(`Error checking content existence for ${contentType}/${slug}/${locale}:`, error)
    return false
  }
}

/**
 * Get content title for display purposes
 *
 * This function retrieves the actual title of a content item in a specific language
 * for display in UI components. It's useful for showing users what content they're
 * viewing or what content is available in different languages.
 *
 * The function now uses the CMS abstraction layer to find the content item and
 * extract its title. This provides a user-friendly way to identify content
 * across different languages while working with any CMS provider.
 *
 * @param contentType - Type of content ('blog', 'product', 'case-study')
 * @param slug - Unique identifier for the content
 * @param locale - Language locale for the content
 * @returns Content title string or null if not found
 */
export async function getContentTitle(
  contentType: ContentType,
  slug: string,
  locale: string
): Promise<string | null> {
  // Early return for non-content types or missing slugs
  if (contentType === 'other' || !slug) {
    return null
  }

  try {
    // Use the CMS abstraction layer to get content title
    return await cms.getContentTitle(contentType, slug, locale)
  } catch (error) {
    console.error(`Error getting content title for ${contentType}/${slug}/${locale}:`, error)
    return null
  }
}
