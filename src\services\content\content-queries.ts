/**
 * Content Queries Service
 * 
 * This service handles all content-related data queries, including checking
 * content existence and retrieving content metadata. It serves as the primary
 * interface between the application and the content data source.
 * 
 * Key Features:
 * - Content existence checking across all supported languages
 * - Content title retrieval for display purposes
 * - Type-safe content access with error handling
 * 
 * FUTURE EXTENSIBILITY - Headless CMS Integration:
 * This service is the primary candidate for the adapter pattern. When migrating
 * to a Headless CMS, these functions would be replaced with API calls while
 * maintaining the same interface contracts.
 * 
 * Migration Strategy:
 * - Replace direct Contentlayer imports with adapter calls
 * - Add caching layer for API responses
 * - Implement error handling for network requests
 * - Maintain the same function signatures for backward compatibility
 */

import type { ContentType } from './types'

// Contentlayer Generated Data - Type-safe content collections
// 
// FUTURE CMS MIGRATION NOTE:
// This is the primary integration point with Contentlayer. When migrating to a
// Headless CMS, these imports should be replaced with a content adapter pattern:
//
// Example future implementation:
// import { contentAdapter } from '@/services/content/adapters'
// const allBlogs = await contentAdapter.getAllBlogs()
// const allProducts = await contentAdapter.getAllProducts()
// const allCaseStudies = await contentAdapter.getAllCaseStudies()
//
// The adapter would handle different CMS backends (Strapi, Sanity, Contentful)
// while maintaining the same data structure and interface.
import { allBlogs, allProducts, allCaseStudies } from 'contentlayer2/generated'

/**
 * Check if content exists in a specific language
 * 
 * This function queries the Contentlayer generated data to determine if a specific
 * piece of content (identified by type and slug) has a translation available in
 * the target language. It's used to determine language availability before
 * attempting navigation.
 * 
 * FUTURE CMS MIGRATION:
 * This function is a prime candidate for the adapter pattern. When migrating to
 * a Headless CMS, this would become:
 * ```typescript
 * return await contentAdapter.contentExists(contentType, slug, locale)
 * ```
 * The adapter would handle API calls, caching, and data transformation while
 * maintaining the same boolean return type.
 * 
 * @param contentType - Type of content to check ('blog', 'product', 'case-study')
 * @param slug - Unique identifier for the content
 * @param locale - Target locale to check (e.g., 'en', 'zh')
 * @returns true if content exists in the target locale, false otherwise
 */
export function contentExistsInLocale(
  contentType: ContentType,
  slug: string,
  locale: string
): boolean {
  // Early return for non-content types or missing slugs
  if (contentType === 'other' || !slug) {
    return false
  }

  // Query the appropriate Contentlayer collection based on content type
  // NOTE: These direct array queries will be replaced with adapter calls in CMS migration
  switch (contentType) {
    case 'blog':
      // Search in the blogs collection for matching slug and language
      return allBlogs.some(blog => blog.slug === slug && blog.lang === locale)
    
    case 'product':
      // Search in the products collection for matching slug and language
      return allProducts.some(product => product.slug === slug && product.lang === locale)
    
    case 'case-study':
      // Search in the case studies collection for matching slug and language
      return allCaseStudies.some(caseStudy => caseStudy.slug === slug && caseStudy.lang === locale)
    
    default:
      // Unknown content type
      return false
  }
}

/**
 * Get content title for display purposes
 * 
 * This function retrieves the actual title of a content item in a specific language
 * for display in UI components. It's useful for showing users what content they're
 * viewing or what content is available in different languages.
 * 
 * The function queries the appropriate Contentlayer collection to find the content
 * item and extract its title. This provides a user-friendly way to identify content
 * across different languages.
 * 
 * FUTURE CMS MIGRATION:
 * This function demonstrates the clean abstraction that makes CMS migration easier.
 * The interface (input parameters and return type) would remain identical, but the
 * implementation would change to:
 * ```typescript
 * return await contentAdapter.getContentTitle(contentType, slug, locale)
 * ```
 * This ensures UI components continue working without modification.
 * 
 * @param contentType - Type of content ('blog', 'product', 'case-study')
 * @param slug - Unique identifier for the content
 * @param locale - Language locale for the content
 * @returns Content title string or null if not found
 */
export function getContentTitle(
  contentType: ContentType,
  slug: string,
  locale: string
): string | null {
  // Early return for non-content types or missing slugs
  if (contentType === 'other' || !slug) {
    return null
  }

  // Query the appropriate Contentlayer collection based on content type
  // NOTE: Direct array access will be replaced with adapter pattern for CMS migration
  switch (contentType) {
    case 'blog':
      // Find the blog post with matching slug and language
      const blog = allBlogs.find(b => b.slug === slug && b.lang === locale)
      return blog?.title || null
    
    case 'product':
      // Find the product with matching slug and language
      const product = allProducts.find(p => p.slug === slug && p.lang === locale)
      return product?.title || null
    
    case 'case-study':
      // Find the case study with matching slug and language
      const caseStudy = allCaseStudies.find(c => c.slug === slug && c.lang === locale)
      return caseStudy?.title || null
    
    default:
      // Unknown content type
      return null
  }
}
