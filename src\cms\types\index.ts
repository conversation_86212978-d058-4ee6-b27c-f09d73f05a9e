/**
 * CMS Types Module
 * 
 * Centralized type definitions for the CMS abstraction layer.
 * This module provides unified interfaces that can be implemented
 * by different CMS providers (Contentlayer2, Strapi, Sanity, etc.).
 * 
 * The types are designed to be provider-agnostic, allowing for
 * seamless switching between different CMS backends while maintaining
 * type safety and consistent API interfaces throughout the application.
 */

// Re-export generated types from Contentlayer2
export type { Blog, Product, CaseStudy } from 'contentlayer2/generated'

/**
 * Content Type Identifiers
 * 
 * Defines the available content types in the system.
 * These identifiers are used throughout the CMS abstraction
 * layer to specify which type of content to operate on.
 */
export type ContentType = 'blog' | 'product' | 'case-study'

/**
 * Base Content Item Interface
 * 
 * Defines the common structure that all content items must have
 * regardless of their specific type. This ensures consistency
 * across different content types and CMS providers.
 */
export interface BaseContentItem {
  // Core identification
  slug: string
  title: string
  lang: string
  url: string
  
  // Content and metadata
  description?: string
  body: {
    raw: string
    html: string
  }
  
  // Media and visual content
  coverImage?: string
  authorImage?: string
  videoUrl?: string
  videoThumbnail?: string
  videoDuration?: string
  
  // Publishing information
  author?: string
  publishedAt?: string
  createdAt: string
  featured: boolean
  tags?: string[]
}

/**
 * Content Item Union Type
 * 
 * Represents any type of content item in the system.
 * This union type allows for type-safe handling of different
 * content types while maintaining their specific properties.
 */
export type ContentItem = BaseContentItem

/**
 * Query Options Interface
 * 
 * Defines the available options for querying content from
 * CMS providers. These options provide flexible filtering,
 * sorting, and pagination capabilities.
 */
export interface QueryOptions {
  // Filtering options
  featured?: boolean
  tags?: string[]
  author?: string
  
  // Sorting options
  sortBy?: 'title' | 'publishedAt' | 'createdAt' | 'featured'
  order?: 'asc' | 'desc'
  
  // Pagination options
  limit?: number
  offset?: number
}

/**
 * Content Metadata Interface
 * 
 * Represents metadata information about content items
 * including computed properties like reading time and
 * word count that are useful for SEO and UX features.
 */
export interface ContentMetadata {
  wordCount: number
  readingTime: number
  publishedAt?: string
  updatedAt?: string
  author?: string
  tags: string[]
}

/**
 * Language Version Interface
 * 
 * Represents different language versions of the same content.
 * This is used for implementing language switching functionality
 * and providing users with alternative language options.
 */
export interface LanguageVersion {
  lang: string
  title: string
  url: string
  available: boolean
}

/**
 * Content Provider Interface
 * 
 * Defines the contract that all CMS providers must implement.
 * This interface ensures that different CMS backends can be
 * used interchangeably while providing consistent functionality.
 */
export interface ContentProvider {
  // Provider identification
  readonly name: string
  readonly version: string
  
  // Core content operations
  getContent<T extends ContentItem>(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<T | null>
  
  getContentList<T extends ContentItem>(
    type: ContentType, 
    locale: string, 
    options?: QueryOptions
  ): Promise<T[]>
  
  contentExists(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<boolean>
  
  // Metadata and utility operations
  getContentTitle(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<string | null>
  
  getContentMetadata(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<ContentMetadata | null>
  
  getAvailableLanguages(
    type: ContentType, 
    slug: string
  ): Promise<LanguageVersion[]>
  
  // Advanced operations
  getRelatedContent?<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit?: number
  ): Promise<T[]>
}

/**
 * CMS Configuration Interface
 * 
 * Defines the configuration options for the CMS system
 * including provider selection, caching settings, and
 * feature flags for different capabilities.
 */
export interface CMSConfig {
  // Provider configuration
  provider: 'contentlayer2' | 'strapi' | 'sanity' | 'contentful' | 'nextjs-mdx'
  
  // Content configuration
  contentTypes: ContentType[]
  defaultLocale: string
  supportedLocales: string[]
  
  // Feature flags
  features: {
    cache: boolean
    seo: boolean
    relatedContent: boolean
    languageSwitching: boolean
  }
  
  // Performance settings
  cache?: {
    enabled: boolean
    ttl: number
    strategy: 'memory' | 'redis' | 'file'
  }
}

/**
 * SEO Data Interface
 * 
 * Represents SEO-related data that can be generated
 * from content items for search engine optimization
 * and social media sharing.
 */
export interface SEOData {
  title: string
  description: string
  canonical: string
  openGraph: {
    title: string
    description: string
    image?: string
    type: 'article' | 'website'
    url: string
  }
  twitter: {
    card: 'summary' | 'summary_large_image'
    title: string
    description: string
    image?: string
  }
  structuredData?: object
}

/**
 * Cache Entry Interface
 * 
 * Represents a cached content item with metadata
 * about when it was cached and when it expires.
 * This is used by the caching layer for performance optimization.
 */
export interface CacheEntry<T = any> {
  data: T
  timestamp: number
  ttl: number
  key: string
}

/**
 * Error Types
 * 
 * Defines specific error types that can be thrown
 * by CMS operations for better error handling and
 * debugging capabilities.
 */
export class CMSError extends Error {
  constructor(
    message: string,
    public code: string,
    public provider?: string
  ) {
    super(message)
    this.name = 'CMSError'
  }
}

export class ContentNotFoundError extends CMSError {
  constructor(type: ContentType, slug: string, locale: string) {
    super(
      `Content not found: ${type}/${locale}/${slug}`,
      'CONTENT_NOT_FOUND'
    )
  }
}

export class ProviderError extends CMSError {
  constructor(message: string, provider: string) {
    super(message, 'PROVIDER_ERROR', provider)
  }
}

/**
 * Utility Types
 * 
 * Helper types for working with content and CMS operations
 * in a type-safe manner throughout the application.
 */
export type ContentTypeMap = {
  blog: BaseContentItem
  product: BaseContentItem
  'case-study': BaseContentItem
}

export type ContentByType<T extends ContentType> = ContentTypeMap[T]

// Re-export everything for convenience
export * from './seo'
export * from './cache'
