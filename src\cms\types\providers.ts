/**
 * CMS Provider Type Definitions
 * 
 * Interfaces and types for CMS provider implementations.
 * These define the contract that all CMS providers must follow.
 */

import type {
  ContentItem,
  ContentType,
  ContentQueryOptions,
  ContentAvailability,
  BlogContent,
  ProductContent,
  CaseStudyContent
} from './content'

// Base CMS provider interface
export interface CMSProvider {
  // Provider identification
  readonly name: string
  readonly version: string
  
  // Content retrieval methods
  getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null>
  
  getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: ContentQueryOptions
  ): Promise<T[]>
  
  // Content existence and metadata
  contentExists(type: ContentType, slug: string, locale: string): Promise<boolean>
  
  getContentTitle(type: ContentType, slug: string, locale: string): Promise<string | null>
  
  getAvailableLanguageVersions(
    type: ContentType,
    slug: string
  ): Promise<ContentAvailability[]>
  
  // Related content
  getRelatedContent<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit?: number
  ): Promise<T[]>
  
  // Provider lifecycle methods
  initialize?(): Promise<void>
  destroy?(): Promise<void>
}

// Content provider factory interface
export interface CMSProviderFactory {
  create(providerName: string, config?: any): CMSProvider
  register(name: string, provider: new (config?: any) => CMSProvider): void
  getAvailableProviders(): string[]
}

// Provider configuration interface
export interface ProviderConfig {
  name: string
  enabled: boolean
  config?: Record<string, any>
}

// CMS provider registry interface
export interface CMSProviderRegistry {
  register(provider: CMSProvider): void
  get(name: string): CMSProvider | undefined
  getActive(): CMSProvider | undefined
  setActive(name: string): void
  list(): string[]
}

// Provider initialization options
export interface ProviderInitOptions {
  development?: boolean
  cache?: boolean
  watch?: boolean
  [key: string]: any
}
