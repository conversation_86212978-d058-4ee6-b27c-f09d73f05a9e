{"version": 3, "file": "custom.d.ts", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/dlp/profiles/custom.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AACzC,OAAO,KAAK,SAAS,MAAM,UAAU,CAAC;AACtC,OAAO,KAAK,WAAW,MAAM,YAAY,CAAC;AAE1C,qBAAa,MAAO,SAAQ,WAAW;IACrC;;;;;;;;;;;;;;;;;;OAkBG;IACH,MAAM,CAAC,MAAM,EAAE,kBAAkB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC;IAUvG;;;;;;;;;;;OAWG;IACH,MAAM,CACJ,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,kBAAkB,EAC1B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC;IAUvC;;;;;;;;;;;OAWG;IACH,MAAM,CACJ,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,kBAAkB,EAC1B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAU/C;;;;;;;;;;;OAWG;IACH,GAAG,CACD,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,eAAe,EACvB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC;CASxC;AAED,MAAM,WAAW,aAAa;IAC5B;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,mBAAmB,EAAE,MAAM,CAAC;IAE5B;;;OAGG;IACH,iBAAiB,EAAE,WAAW,CAAC,gBAAgB,CAAC;IAEhD;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB,OAAO,EAAE,KAAK,CACV,aAAa,CAAC,WAAW,GACzB,aAAa,CAAC,eAAe,GAC7B,aAAa,CAAC,gBAAgB,GAC9B,aAAa,CAAC,cAAc,GAC5B,aAAa,CAAC,wBAAwB,GACtC,aAAa,CAAC,aAAa,CAC9B,CAAC;IAEF;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb,WAAW,EAAE,OAAO,CAAC;IAErB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAE7B,oBAAoB,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,WAAW,CAAC;IAE/D;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B;AAED,yBAAiB,aAAa,CAAC;IAC7B,UAAiB,WAAW;QAC1B,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,OAAO,EAAE,SAAS,CAAC,OAAO,CAAC;QAE3B,IAAI,EAAE,QAAQ,CAAC;QAEf,UAAU,EAAE,MAAM,CAAC;QAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC5B;IAED,UAAiB,eAAe;QAC9B,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,eAAe,CAAC,UAAU,CAAC;QAEvC,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,YAAY,CAAC;QAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC5B;IAED,UAAiB,eAAe,CAAC;QAC/B,UAAiB,UAAU;YACzB;;eAEG;YACH,oBAAoB,EAAE,OAAO,CAAC;YAE9B;;;eAGG;YACH,SAAS,EAAE,OAAO,CAAC;SACpB;KACF;IAED,UAAiB,gBAAgB;QAC/B,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,aAAa,CAAC;QAEpB,UAAU,EAAE,MAAM,CAAC;QAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC5B;IAED,UAAiB,cAAc;QAC7B,EAAE,EAAE,MAAM,CAAC;QAEX;;;WAGG;QACH,cAAc,EAAE,OAAO,CAAC;QAExB,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,MAAM,EAAE,OAAO,CAAC;QAEhB,IAAI,EAAE,YAAY,CAAC;QAEnB,UAAU,EAAE,MAAM,CAAC;KACpB;IAED,UAAiB,wBAAwB;QACvC,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,sBAAsB,CAAC;QAE7B,UAAU,EAAE,MAAM,CAAC;KACpB;IAED,UAAiB,aAAa;QAC5B,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,WAAW,CAAC;QAElB,UAAU,EAAE,MAAM,CAAC;QAEnB,SAAS,EAAE,OAAO,CAAC;QAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC5B;CACF;AAED,MAAM,WAAW,OAAO;IACtB,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,YAAY;IAC3B,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,MAAM,oBAAoB,GAAG,OAAO,CAAC;AAE3C,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,OAAO,EAAE,KAAK,CAAC,kBAAkB,CAAC,iBAAiB,GAAG,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;IAE9F;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAE7B;;;OAGG;IACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAE7B;;OAEG;IACH,oBAAoB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAErC;;;OAGG;IACH,iBAAiB,CAAC,EAAE,WAAW,CAAC,qBAAqB,CAAC;IAEtD;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5B;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IAEtB;;;OAGG;IACH,cAAc,CAAC,EAAE,KAAK,CAClB,kBAAkB,CAAC,MAAM,GACzB,kBAAkB,CAAC,UAAU,GAC7B,kBAAkB,CAAC,WAAW,GAC9B,kBAAkB,CAAC,SAAS,GAC5B,kBAAkB,CAAC,YAAY,CAClC,CAAC;CACH;AAED,yBAAiB,kBAAkB,CAAC;IAClC,UAAiB,iBAAiB;QAChC,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,OAAO,EAAE,SAAS,CAAC,YAAY,CAAC;KACjC;IAED,UAAiB,mBAAmB;QAClC,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;KACtB;IAED,UAAiB,MAAM;QACrB,OAAO,EAAE,OAAO,CAAC;QAEjB,QAAQ,EAAE,MAAM,CAAC;QAEjB,UAAU,EAAE,QAAQ,CAAC;KACtB;IAED,UAAiB,UAAU;QACzB,OAAO,EAAE,OAAO,CAAC;QAEjB,QAAQ,EAAE,MAAM,CAAC;QAEjB,UAAU,EAAE,YAAY,CAAC;KAC1B;IAED,UAAiB,WAAW;QAC1B,OAAO,EAAE,OAAO,CAAC;QAEjB,QAAQ,EAAE,MAAM,CAAC;QAEjB,UAAU,EAAE,aAAa,CAAC;KAC3B;IAED,UAAiB,SAAS;QACxB,OAAO,EAAE,OAAO,CAAC;QAEjB,QAAQ,EAAE,MAAM,CAAC;QAEjB,UAAU,EAAE,YAAY,CAAC;KAC1B;IAED,UAAiB,YAAY;QAC3B,OAAO,EAAE,OAAO,CAAC;QAEjB,QAAQ,EAAE,MAAM,CAAC;QAEjB,UAAU,EAAE,sBAAsB,CAAC;KACpC;CACF;AAED,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAE7B;;OAEG;IACH,mBAAmB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEpC;;OAEG;IACH,oBAAoB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAErC;;;OAGG;IACH,iBAAiB,CAAC,EAAE,WAAW,CAAC,qBAAqB,CAAC;IAEtD;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5B;;;OAGG;IACH,OAAO,CAAC,EAAE,KAAK,CAAC,kBAAkB,CAAC,uBAAuB,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;IAE1G;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IAEtB;;OAEG;IACH,cAAc,CAAC,EAAE,KAAK,CAClB,kBAAkB,CAAC,UAAU,GAC7B,kBAAkB,CAAC,WAAW,GAC9B,kBAAkB,CAAC,SAAS,GAC5B,kBAAkB,CAAC,YAAY,CAClC,CAAC;CACH;AAED,yBAAiB,kBAAkB,CAAC;IAClC,UAAiB,uBAAuB;QACtC,OAAO,EAAE,OAAO,CAAC;QAEjB,QAAQ,EAAE,MAAM,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,OAAO,EAAE,SAAS,CAAC,YAAY,CAAC;KACjC;IAED,UAAiB,iBAAiB;QAChC,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,OAAO,EAAE,SAAS,CAAC,YAAY,CAAC;KACjC;IAED,UAAiB,UAAU;QACzB,OAAO,EAAE,OAAO,CAAC;QAEjB,QAAQ,EAAE,MAAM,CAAC;QAEjB,UAAU,EAAE,YAAY,CAAC;KAC1B;IAED,UAAiB,WAAW;QAC1B,OAAO,EAAE,OAAO,CAAC;QAEjB,QAAQ,EAAE,MAAM,CAAC;QAEjB,UAAU,EAAE,aAAa,CAAC;KAC3B;IAED,UAAiB,SAAS;QACxB,OAAO,EAAE,OAAO,CAAC;QAEjB,QAAQ,EAAE,MAAM,CAAC;QAEjB,UAAU,EAAE,YAAY,CAAC;KAC1B;IAED,UAAiB,YAAY;QAC3B,OAAO,EAAE,OAAO,CAAC;QAEjB,QAAQ,EAAE,MAAM,CAAC;QAEjB,UAAU,EAAE,sBAAsB,CAAC;KACpC;CACF;AAED,MAAM,WAAW,kBAAkB;IACjC,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,eAAe;IAC9B,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,OAAO,EACL,KAAK,aAAa,IAAI,aAAa,EACnC,KAAK,OAAO,IAAI,OAAO,EACvB,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,eAAe,IAAI,eAAe,GACxC,CAAC;CACH"}