"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Scripts = exports.Namespaces = exports.NamespaceListResponsesSinglePage = void 0;
var namespaces_1 = require("./namespaces.js");
Object.defineProperty(exports, "NamespaceListResponsesSinglePage", { enumerable: true, get: function () { return namespaces_1.NamespaceListResponsesSinglePage; } });
Object.defineProperty(exports, "Namespaces", { enumerable: true, get: function () { return namespaces_1.Namespaces; } });
var index_1 = require("./scripts/index.js");
Object.defineProperty(exports, "Scripts", { enumerable: true, get: function () { return index_1.Scripts; } });
//# sourceMappingURL=index.js.map