/**
 * SEO Type Definitions
 * 
 * Types for SEO functionality including sitemap, RSS, and structured data.
 */

import type { ContentItem, ContentType } from './content'

// Sitemap entry interface
export interface SitemapEntry {
  url: string
  lastmod?: string
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: number
  alternates?: Array<{
    hreflang: string
    href: string
  }>
}

// RSS feed interface
export interface RSSFeed {
  title: string
  description: string
  link: string
  language: string
  lastBuildDate: string
  items: RSSItem[]
}

// RSS item interface
export interface RSSItem {
  title: string
  description: string
  link: string
  guid: string
  pubDate: string
  author?: string
  category?: string[]
}

// Structured data interfaces
export interface StructuredData {
  '@context': string
  '@type': string
  [key: string]: any
}

export interface BlogPostStructuredData extends StructuredData {
  '@type': 'BlogPosting'
  headline: string
  description: string
  author: {
    '@type': 'Person'
    name: string
    image?: {
      '@type': 'ImageObject'
      url: string
    }
  }
  datePublished: string
  dateModified?: string
  image?: {
    '@type': 'ImageObject'
    url: string
    width: number
    height: number
  }
  url: string
  publisher: OrganizationStructuredData
  mainEntityOfPage: {
    '@type': 'WebPage'
    '@id': string
  }
  keywords?: string
  wordCount?: number
  timeRequired?: string
}

export interface ProductStructuredData extends StructuredData {
  '@type': 'SoftwareApplication'
  name: string
  description: string
  url: string
  image?: string
  applicationCategory: string
  operatingSystem: string
  offers?: {
    '@type': 'Offer'
    price: string
    priceCurrency: string
  }
  publisher: OrganizationStructuredData
}

export interface OrganizationStructuredData extends StructuredData {
  '@type': 'Organization'
  name: string
  url: string
  logo: {
    '@type': 'ImageObject'
    url: string
  }
  description?: string
  sameAs?: string[]
}

export interface BreadcrumbStructuredData extends StructuredData {
  '@type': 'BreadcrumbList'
  itemListElement: Array<{
    '@type': 'ListItem'
    position: number
    name: string
    item: string
  }>
}

// SEO generator interfaces
export interface SEOGenerator {
  generateSitemap(content: ContentItem[]): SitemapEntry[]
  generateRSSFeeds(blogs: ContentItem[]): Record<string, RSSFeed>
  generateStructuredData(content: ContentItem): StructuredData
}

// Meta tag interfaces
export interface MetaTags {
  title: string
  description: string
  canonical?: string
  openGraph: OpenGraphTags
  twitter: TwitterCardTags
  alternates?: Array<{
    hreflang: string
    href: string
  }>
}

export interface OpenGraphTags {
  title: string
  description: string
  type: 'website' | 'article' | 'product'
  url: string
  images?: Array<{
    url: string
    width: number
    height: number
    alt: string
  }>
  publishedTime?: string
  modifiedTime?: string
  authors?: string[]
  tags?: string[]
}

export interface TwitterCardTags {
  card: 'summary' | 'summary_large_image' | 'app' | 'player'
  title: string
  description: string
  images?: string[]
  creator?: string
  site?: string
}
