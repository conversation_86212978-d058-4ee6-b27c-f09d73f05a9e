/**
 * CMS Providers Module
 * 
 * Central registry and factory for all CMS providers.
 * This module manages provider registration, instantiation, and lifecycle.
 */

// Export base provider
export { BaseCMSProvider } from './base'

// Export current providers
export { VeliteProvider } from './velite'

// Export placeholder providers (for future implementation)
export { default as StrapiProvider } from './strapi'
export { default as SanityProvider } from './sanity'
export { default as ContentfulProvider } from './contentful'
export { default as NextMDXRemoteProvider } from './next-mdx-remote'

// Provider registry and factory
import type { CMSProvider, CMSProviderFactory, CMSProviderRegistry } from '../types'

/**
 * CMS Provider Factory
 * 
 * Factory class for creating and managing CMS provider instances.
 */
export class ProviderFactory implements CMSProviderFactory {
  private providers = new Map<string, new (config?: any) => CMSProvider>()
  
  register(name: string, provider: new (config?: any) => CMSProvider): void {
    this.providers.set(name, provider)
  }
  
  create(providerName: string, config?: any): CMSProvider {
    const ProviderClass = this.providers.get(providerName)
    if (!ProviderClass) {
      throw new Error(`Unknown CMS provider: ${providerName}`)
    }
    
    return new ProviderClass(config)
  }
  
  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys())
  }
}

/**
 * CMS Provider Registry
 * 
 * Registry for managing active provider instances.
 */
export class ProviderRegistry implements CMSProviderRegistry {
  private providers = new Map<string, CMSProvider>()
  private activeProvider: string | null = null
  
  register(provider: CMSProvider): void {
    this.providers.set(provider.name, provider)
  }
  
  get(name: string): CMSProvider | undefined {
    return this.providers.get(name)
  }
  
  getActive(): CMSProvider | undefined {
    return this.activeProvider ? this.providers.get(this.activeProvider) : undefined
  }
  
  setActive(name: string): void {
    if (!this.providers.has(name)) {
      throw new Error(`Provider ${name} is not registered`)
    }
    this.activeProvider = name
  }
  
  list(): string[] {
    return Array.from(this.providers.keys())
  }
}

// Global instances
export const providerFactory = new ProviderFactory()
export const providerRegistry = new ProviderRegistry()

// Register providers function
async function registerProviders() {
  try {
    // Register Velite provider
    const { VeliteProvider } = await import('./velite')
    providerFactory.register('velite', VeliteProvider)

    // Register placeholder providers (not yet functional)
    const { default: StrapiProvider } = await import('./strapi')
    const { default: SanityProvider } = await import('./sanity')
    const { default: ContentfulProvider } = await import('./contentful')
    const { default: NextMDXRemoteProvider } = await import('./next-mdx-remote')

    providerFactory.register('strapi', StrapiProvider)
    providerFactory.register('sanity', SanityProvider)
    providerFactory.register('contentful', ContentfulProvider)
    providerFactory.register('next-mdx-remote', NextMDXRemoteProvider)
  } catch (error) {
    // Providers will be registered when they are implemented
    console.warn('Some CMS providers are not yet available:', error)
  }
}

// Initialize providers
registerProviders().catch(console.error)

/**
 * Utility function to create a provider instance
 */
export function createProvider(name: string, config?: any): CMSProvider {
  return providerFactory.create(name, config)
}

/**
 * Utility function to get available provider names
 */
export function getAvailableProviders(): string[] {
  return providerFactory.getAvailableProviders()
}
