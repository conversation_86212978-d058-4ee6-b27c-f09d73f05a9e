{"version": 3, "file": "profiles.d.ts", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/dlp/profiles/profiles.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AACzC,OAAO,KAAK,WAAW,MAAM,YAAY,CAAC;AAC1C,OAAO,KAAK,SAAS,MAAM,UAAU,CAAC;AACtC,OAAO,EACL,MAAM,EACN,kBAAkB,EAClB,kBAAkB,EAClB,oBAAoB,EACpB,eAAe,EACf,aAAa,IAAI,sBAAsB,EACvC,kBAAkB,EAClB,OAAO,EACR,MAAM,UAAU,CAAC;AAClB,OAAO,KAAK,aAAa,MAAM,cAAc,CAAC;AAC9C,OAAO,EACL,UAAU,EACV,sBAAsB,EACtB,sBAAsB,EACtB,wBAAwB,EACxB,mBAAmB,EACnB,iBAAiB,IAAI,8BAA8B,EACnD,sBAAsB,EACvB,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AAEpD,qBAAa,QAAS,SAAQ,WAAW;IACvC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAsC;IAC9D,UAAU,EAAE,aAAa,CAAC,UAAU,CAA8C;IAElF;;;;;;;;;;;;OAYG;IACH,IAAI,CACF,MAAM,EAAE,iBAAiB,EACzB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,OAAO,CAAC;IAQhD;;;;;;;;;;OAUG;IACH,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;CAQ1G;AAED,qBAAa,kBAAmB,SAAQ,UAAU,CAAC,OAAO,CAAC;CAAG;AAE9D;;;GAGG;AACH,MAAM,WAAW,gBAAgB;IAC/B;;;OAGG;IACH,OAAO,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,IAAI,EAAE,iBAAiB,CAAC;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,qBAAqB;IACpC;;;OAGG;IACH,OAAO,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,IAAI,EAAE,sBAAsB,CAAC;CAC9B;AAED,MAAM,MAAM,OAAO,GAAG,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,kBAAkB,CAAC;AAErG,yBAAiB,OAAO,CAAC;IACvB,UAAiB,aAAa;QAC5B;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;QAEX;;WAEG;QACH,mBAAmB,EAAE,MAAM,CAAC;QAE5B;;;WAGG;QACH,iBAAiB,EAAE,WAAW,CAAC,gBAAgB,CAAC;QAEhD;;WAEG;QACH,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,KAAK,CACV,aAAa,CAAC,WAAW,GACzB,aAAa,CAAC,eAAe,GAC7B,aAAa,CAAC,gBAAgB,GAC9B,aAAa,CAAC,cAAc,GAC5B,aAAa,CAAC,wBAAwB,GACtC,aAAa,CAAC,aAAa,CAC9B,CAAC;QAEF;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;QAEb,WAAW,EAAE,OAAO,CAAC;QAErB,IAAI,EAAE,QAAQ,CAAC;QAEf;;WAEG;QACH,UAAU,EAAE,MAAM,CAAC;QAEnB,kBAAkB,CAAC,EAAE,OAAO,CAAC;QAE7B,oBAAoB,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,WAAW,CAAC;QAE/D;;WAEG;QACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC7B;IAED,UAAiB,aAAa,CAAC;QAC7B,UAAiB,WAAW;YAC1B,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,OAAO,EAAE,SAAS,CAAC,OAAO,CAAC;YAE3B,IAAI,EAAE,QAAQ,CAAC;YAEf,UAAU,EAAE,MAAM,CAAC;YAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SAC5B;QAED,UAAiB,eAAe;YAC9B,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,eAAe,CAAC,UAAU,CAAC;YAEvC,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,IAAI,EAAE,YAAY,CAAC;YAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SAC5B;QAED,UAAiB,eAAe,CAAC;YAC/B,UAAiB,UAAU;gBACzB;;mBAEG;gBACH,oBAAoB,EAAE,OAAO,CAAC;gBAE9B;;;mBAGG;gBACH,SAAS,EAAE,OAAO,CAAC;aACpB;SACF;QAED,UAAiB,gBAAgB;YAC/B,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,IAAI,EAAE,aAAa,CAAC;YAEpB,UAAU,EAAE,MAAM,CAAC;YAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SAC5B;QAED,UAAiB,cAAc;YAC7B,EAAE,EAAE,MAAM,CAAC;YAEX;;;eAGG;YACH,cAAc,EAAE,OAAO,CAAC;YAExB,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,MAAM,EAAE,OAAO,CAAC;YAEhB,IAAI,EAAE,YAAY,CAAC;YAEnB,UAAU,EAAE,MAAM,CAAC;SACpB;QAED,UAAiB,wBAAwB;YACvC,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,IAAI,EAAE,sBAAsB,CAAC;YAE7B,UAAU,EAAE,MAAM,CAAC;SACpB;QAED,UAAiB,aAAa;YAC5B,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,IAAI,EAAE,WAAW,CAAC;YAElB,UAAU,EAAE,MAAM,CAAC;YAEnB,SAAS,EAAE,OAAO,CAAC;YAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SAC5B;KACF;IAED,UAAiB,iBAAiB;QAChC;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;QAEX,mBAAmB,EAAE,MAAM,CAAC;QAE5B,OAAO,EAAE,KAAK,CACV,iBAAiB,CAAC,WAAW,GAC7B,iBAAiB,CAAC,eAAe,GACjC,iBAAiB,CAAC,gBAAgB,GAClC,iBAAiB,CAAC,cAAc,GAChC,iBAAiB,CAAC,wBAAwB,GAC1C,iBAAiB,CAAC,aAAa,CAClC,CAAC;QAEF;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,YAAY,CAAC;QAEnB,kBAAkB,CAAC,EAAE,OAAO,CAAC;QAE7B,oBAAoB,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,WAAW,CAAC;QAE/D;;;WAGG;QACH,iBAAiB,CAAC,EAAE,WAAW,CAAC,gBAAgB,CAAC;QAEjD,WAAW,CAAC,EAAE,OAAO,CAAC;QAEtB;;WAEG;QACH,WAAW,CAAC,EAAE,OAAO,CAAC;KACvB;IAED,UAAiB,iBAAiB,CAAC;QACjC,UAAiB,WAAW;YAC1B,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,OAAO,EAAE,SAAS,CAAC,OAAO,CAAC;YAE3B,IAAI,EAAE,QAAQ,CAAC;YAEf,UAAU,EAAE,MAAM,CAAC;YAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SAC5B;QAED,UAAiB,eAAe;YAC9B,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,eAAe,CAAC,UAAU,CAAC;YAEvC,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,IAAI,EAAE,YAAY,CAAC;YAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SAC5B;QAED,UAAiB,eAAe,CAAC;YAC/B,UAAiB,UAAU;gBACzB;;mBAEG;gBACH,oBAAoB,EAAE,OAAO,CAAC;gBAE9B;;;mBAGG;gBACH,SAAS,EAAE,OAAO,CAAC;aACpB;SACF;QAED,UAAiB,gBAAgB;YAC/B,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,IAAI,EAAE,aAAa,CAAC;YAEpB,UAAU,EAAE,MAAM,CAAC;YAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SAC5B;QAED,UAAiB,cAAc;YAC7B,EAAE,EAAE,MAAM,CAAC;YAEX;;;eAGG;YACH,cAAc,EAAE,OAAO,CAAC;YAExB,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,MAAM,EAAE,OAAO,CAAC;YAEhB,IAAI,EAAE,YAAY,CAAC;YAEnB,UAAU,EAAE,MAAM,CAAC;SACpB;QAED,UAAiB,wBAAwB;YACvC,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,IAAI,EAAE,sBAAsB,CAAC;YAE7B,UAAU,EAAE,MAAM,CAAC;SACpB;QAED,UAAiB,aAAa;YAC5B,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,IAAI,EAAE,WAAW,CAAC;YAElB,UAAU,EAAE,MAAM,CAAC;YAEnB,SAAS,EAAE,OAAO,CAAC;YAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SAC5B;KACF;IAED,UAAiB,kBAAkB;QACjC,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,KAAK,CACV,kBAAkB,CAAC,WAAW,GAC9B,kBAAkB,CAAC,eAAe,GAClC,kBAAkB,CAAC,gBAAgB,GACnC,kBAAkB,CAAC,cAAc,GACjC,kBAAkB,CAAC,wBAAwB,GAC3C,kBAAkB,CAAC,aAAa,CACnC,CAAC;QAEF,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,aAAa,CAAC;QAEpB,UAAU,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC7B;IAED,UAAiB,kBAAkB,CAAC;QAClC,UAAiB,WAAW;YAC1B,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,OAAO,EAAE,SAAS,CAAC,OAAO,CAAC;YAE3B,IAAI,EAAE,QAAQ,CAAC;YAEf,UAAU,EAAE,MAAM,CAAC;YAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SAC5B;QAED,UAAiB,eAAe;YAC9B,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,eAAe,CAAC,UAAU,CAAC;YAEvC,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,IAAI,EAAE,YAAY,CAAC;YAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SAC5B;QAED,UAAiB,eAAe,CAAC;YAC/B,UAAiB,UAAU;gBACzB;;mBAEG;gBACH,oBAAoB,EAAE,OAAO,CAAC;gBAE9B;;;mBAGG;gBACH,SAAS,EAAE,OAAO,CAAC;aACpB;SACF;QAED,UAAiB,gBAAgB;YAC/B,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,IAAI,EAAE,aAAa,CAAC;YAEpB,UAAU,EAAE,MAAM,CAAC;YAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SAC5B;QAED,UAAiB,cAAc;YAC7B,EAAE,EAAE,MAAM,CAAC;YAEX;;;eAGG;YACH,cAAc,EAAE,OAAO,CAAC;YAExB,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,MAAM,EAAE,OAAO,CAAC;YAEhB,IAAI,EAAE,YAAY,CAAC;YAEnB,UAAU,EAAE,MAAM,CAAC;SACpB;QAED,UAAiB,wBAAwB;YACvC,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,IAAI,EAAE,sBAAsB,CAAC;YAE7B,UAAU,EAAE,MAAM,CAAC;SACpB;QAED,UAAiB,aAAa;YAC5B,EAAE,EAAE,MAAM,CAAC;YAEX,UAAU,EAAE,MAAM,CAAC;YAEnB,OAAO,EAAE,OAAO,CAAC;YAEjB,IAAI,EAAE,MAAM,CAAC;YAEb,IAAI,EAAE,WAAW,CAAC;YAElB,UAAU,EAAE,MAAM,CAAC;YAEnB,SAAS,EAAE,OAAO,CAAC;YAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;SAC5B;KACF;CACF;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,KAAK,EAAE,OAAO,CAAC;CAChB;AAED;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,KAAK,EAAE,OAAO,CAAC;CAChB;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;OAGG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;CACf;AAED,MAAM,WAAW,gBAAgB;IAC/B,UAAU,EAAE,MAAM,CAAC;CACpB;AAMD,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,OAAO,EACL,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,OAAO,IAAI,OAAO,EACvB,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,kBAAkB,IAAI,kBAAkB,EACxC,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,gBAAgB,IAAI,gBAAgB,GAC1C,CAAC;IAEF,OAAO,EACL,MAAM,IAAI,MAAM,EAChB,KAAK,sBAAsB,IAAI,aAAa,EAC5C,KAAK,OAAO,IAAI,OAAO,EACvB,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,eAAe,IAAI,eAAe,GACxC,CAAC;IAEF,OAAO,EACL,UAAU,IAAI,UAAU,EACxB,KAAK,8BAA8B,IAAI,iBAAiB,EACxD,KAAK,wBAAwB,IAAI,wBAAwB,EACzD,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,mBAAmB,IAAI,mBAAmB,GAChD,CAAC;CACH"}