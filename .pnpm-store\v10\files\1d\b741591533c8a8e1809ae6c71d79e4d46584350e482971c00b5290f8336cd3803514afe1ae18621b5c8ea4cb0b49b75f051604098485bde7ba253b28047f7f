import { APIResource } from "../../../../resource.js";
import * as SummaryAPI from "./summary.js";
import { Summary, SummaryARCParams, SummaryARCResponse, SummaryDKIMParams, SummaryDKIMResponse, SummaryDMARCParams, SummaryDMARCResponse, SummaryMaliciousParams, SummaryMaliciousResponse, SummarySPFParams, SummarySPFResponse, SummarySpamParams, SummarySpamResponse, SummarySpoofParams, SummarySpoofResponse, SummaryTLSVersionParams, SummaryTLSVersionResponse, SummaryThreatCategoryParams, SummaryThreatCategoryResponse } from "./summary.js";
import * as TimeseriesGroupsAPI from "./timeseries-groups.js";
import { TimeseriesGroupARCParams, TimeseriesGroupARCResponse, TimeseriesGroupDKIMParams, TimeseriesGroupDKIMResponse, TimeseriesGroupDMARCParams, TimeseriesGroupDMARCResponse, TimeseriesGroupMaliciousParams, TimeseriesGroupMaliciousResponse, TimeseriesGroupSPFParams, TimeseriesGroupSPFResponse, TimeseriesGroupSpamParams, TimeseriesGroupSpamResponse, TimeseriesGroupSpoofParams, TimeseriesGroupSpoofResponse, TimeseriesGroupTLSVersionParams, TimeseriesGroupTLSVersionResponse, TimeseriesGroupThreatCategoryParams, TimeseriesGroupThreatCategoryResponse, TimeseriesGroups } from "./timeseries-groups.js";
import * as TopAPI from "./top/top.js";
import { Top } from "./top/top.js";
export declare class Security extends APIResource {
    top: TopAPI.Top;
    summary: SummaryAPI.Summary;
    timeseriesGroups: TimeseriesGroupsAPI.TimeseriesGroups;
}
export declare namespace Security {
    export { Top as Top };
    export { Summary as Summary, type SummaryARCResponse as SummaryARCResponse, type SummaryDKIMResponse as SummaryDKIMResponse, type SummaryDMARCResponse as SummaryDMARCResponse, type SummaryMaliciousResponse as SummaryMaliciousResponse, type SummarySpamResponse as SummarySpamResponse, type SummarySPFResponse as SummarySPFResponse, type SummarySpoofResponse as SummarySpoofResponse, type SummaryThreatCategoryResponse as SummaryThreatCategoryResponse, type SummaryTLSVersionResponse as SummaryTLSVersionResponse, type SummaryARCParams as SummaryARCParams, type SummaryDKIMParams as SummaryDKIMParams, type SummaryDMARCParams as SummaryDMARCParams, type SummaryMaliciousParams as SummaryMaliciousParams, type SummarySpamParams as SummarySpamParams, type SummarySPFParams as SummarySPFParams, type SummarySpoofParams as SummarySpoofParams, type SummaryThreatCategoryParams as SummaryThreatCategoryParams, type SummaryTLSVersionParams as SummaryTLSVersionParams, };
    export { TimeseriesGroups as TimeseriesGroups, type TimeseriesGroupARCResponse as TimeseriesGroupARCResponse, type TimeseriesGroupDKIMResponse as TimeseriesGroupDKIMResponse, type TimeseriesGroupDMARCResponse as TimeseriesGroupDMARCResponse, type TimeseriesGroupMaliciousResponse as TimeseriesGroupMaliciousResponse, type TimeseriesGroupSpamResponse as TimeseriesGroupSpamResponse, type TimeseriesGroupSPFResponse as TimeseriesGroupSPFResponse, type TimeseriesGroupSpoofResponse as TimeseriesGroupSpoofResponse, type TimeseriesGroupThreatCategoryResponse as TimeseriesGroupThreatCategoryResponse, type TimeseriesGroupTLSVersionResponse as TimeseriesGroupTLSVersionResponse, type TimeseriesGroupARCParams as TimeseriesGroupARCParams, type TimeseriesGroupDKIMParams as TimeseriesGroupDKIMParams, type TimeseriesGroupDMARCParams as TimeseriesGroupDMARCParams, type TimeseriesGroupMaliciousParams as TimeseriesGroupMaliciousParams, type TimeseriesGroupSpamParams as TimeseriesGroupSpamParams, type TimeseriesGroupSPFParams as TimeseriesGroupSPFParams, type TimeseriesGroupSpoofParams as TimeseriesGroupSpoofParams, type TimeseriesGroupThreatCategoryParams as TimeseriesGroupThreatCategoryParams, type TimeseriesGroupTLSVersionParams as TimeseriesGroupTLSVersionParams, };
}
//# sourceMappingURL=security.d.ts.map