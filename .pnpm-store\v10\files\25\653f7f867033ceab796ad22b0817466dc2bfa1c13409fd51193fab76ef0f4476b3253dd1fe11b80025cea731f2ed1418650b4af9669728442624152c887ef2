"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomCertificate = void 0;
const resource_1 = require("../../../../resource.js");
class CustomCertificate extends resource_1.APIResource {
    /**
     * Fetches the current Zero Trust certificate configuration.
     *
     * @deprecated
     */
    get(params, options) {
        const { account_id } = params;
        return this._client.get(`/accounts/${account_id}/gateway/configuration/custom_certificate`, options);
    }
}
exports.CustomCertificate = CustomCertificate;
//# sourceMappingURL=custom-certificate.js.map