{"version": 3, "file": "user-policy-checks.d.ts", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/access/applications/user-policy-checks.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AAEnD,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AACzC,OAAO,KAAK,mBAAmB,MAAM,sBAAsB,CAAC;AAC5D,OAAO,KAAK,eAAe,MAAM,gBAAgB,CAAC;AAGlD,qBAAa,gBAAiB,SAAQ,WAAW;IAC/C;;;;;;;;;;;OAWG;IACH,IAAI,CACF,KAAK,EAAE,eAAe,CAAC,UAAU,EACjC,MAAM,CAAC,EAAE,yBAAyB,EAClC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,2BAA2B,CAAC;IAC/C,IAAI,CACF,KAAK,EAAE,eAAe,CAAC,UAAU,EACjC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,2BAA2B,CAAC;CAiChD;AAED,MAAM,WAAW,kBAAkB;IACjC,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,2BAA2B;IAC1C,SAAS,CAAC,EAAE,2BAA2B,CAAC,QAAQ,CAAC;IAEjD,aAAa,CAAC,EAAE,2BAA2B,CAAC,YAAY,CAAC;CAC1D;AAED,yBAAiB,2BAA2B,CAAC;IAC3C,UAAiB,QAAQ;QACvB;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,CAAC;QAEjB,GAAG,CAAC,EAAE,MAAM,CAAC;QAEb,QAAQ,CAAC,EAAE,MAAM,CAAC;QAElB,IAAI,CAAC,EAAE,MAAM,CAAC;QAEd,QAAQ,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAE1B,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB;IAED,UAAiB,YAAY;QAC3B,EAAE,CAAC,EAAE,MAAM,CAAC;QAEZ,UAAU,CAAC,EAAE,MAAM,CAAC;QAEpB,eAAe,CAAC,EAAE,OAAO,CAAC;QAE1B,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf,GAAG,CAAC,EAAE,mBAAmB,CAAC,kBAAkB,CAAC;QAE7C,GAAG,CAAC,EAAE,MAAM,CAAC;QAEb,UAAU,CAAC,EAAE,OAAO,CAAC;QAErB,OAAO,CAAC,EAAE,OAAO,CAAC;QAElB,IAAI,CAAC,EAAE,MAAM,CAAC;QAEd;;WAEG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QAEnB,OAAO,CAAC,EAAE,MAAM,CAAC;KAClB;CACF;AAED,MAAM,WAAW,yBAAyB;IACxC;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,CAAC,OAAO,WAAW,gBAAgB,CAAC;IACxC,OAAO,EACL,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,2BAA2B,IAAI,2BAA2B,EAC/D,KAAK,yBAAyB,IAAI,yBAAyB,GAC5D,CAAC;CACH"}