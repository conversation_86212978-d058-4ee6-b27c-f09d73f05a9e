// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import { isRequestOptions } from "../../../../core.mjs";
export class Summary extends APIResource {
    bitrate(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.bitrate({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/bitrate', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
    duration(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.duration({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/duration', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
    industry(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.industry({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/industry', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
    ipVersion(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.ipVersion({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/ip_version', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
    protocol(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.protocol({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/protocol', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
    vector(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.vector({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/vector', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
    vertical(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.vertical({}, query);
        }
        return this._client.get('/radar/attacks/layer3/summary/vertical', { query, ...options })._thenUnwrap((obj) => obj.result);
    }
}
//# sourceMappingURL=summary.mjs.map