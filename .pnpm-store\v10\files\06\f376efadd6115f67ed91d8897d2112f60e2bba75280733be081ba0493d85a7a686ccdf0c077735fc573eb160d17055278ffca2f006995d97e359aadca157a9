import { APIResource } from "../../../../resource.js";
import * as ContentListsAPI from "./content-lists/content-lists.js";
import { ContentList, ContentListGetParams, ContentListUpdateParams, ContentLists } from "./content-lists/content-lists.js";
export declare class IPFSUniversalPaths extends APIResource {
    contentLists: ContentListsAPI.ContentLists;
}
export declare namespace IPFSUniversalPaths {
    export { ContentLists as ContentLists, type ContentList as ContentList, type ContentListUpdateParams as ContentListUpdateParams, type ContentListGetParams as ContentListGetParams, };
}
//# sourceMappingURL=ipfs-universal-paths.d.ts.map