{"version": 3, "file": "routes.mjs", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/networks/routes/routes.ts"], "names": [], "mappings": "AAAA,sFAAsF;OAE/E,EAAE,WAAW,EAAE;OAEf,KAAK,MAAM;OACX,EAAe,GAAG,EAAE;OACpB,KAAK,WAAW;OAChB,EAA+D,QAAQ,EAAE;OACzE,EAAE,qBAAqB,EAAoC;AAElE,MAAM,OAAO,MAAO,SAAQ,WAAW;IAAvC;;QACE,QAAG,GAAe,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,aAAQ,GAAyB,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IA8G1E,CAAC;IA5GC;;;;;;;;;;;;;OAaG;IACH,MAAM,CAAC,MAAyB,EAAE,OAA6B;QAC7D,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QACvC,OACE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,UAAU,iBAAiB,EAAE,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,CAGjF,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,IAAI,CACF,MAAuB,EACvB,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,CAAC;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,UAAU,iBAAiB,EAAE,6BAA6B,EAAE;YACtG,KAAK;YACL,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACH,MAAM,CAAC,OAAe,EAAE,MAAyB,EAAE,OAA6B;QAC9E,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QAC9B,OACE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,UAAU,mBAAmB,OAAO,EAAE,EAAE,OAAO,CAGjF,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;OAWG;IACH,IAAI,CAAC,OAAe,EAAE,MAAuB,EAAE,OAA6B;QAC1E,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QACvC,OACE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,UAAU,mBAAmB,OAAO,EAAE,EAAE;YACtE,IAAI;YACJ,GAAG,OAAO;SACX,CACF,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;OAUG;IACH,GAAG,CAAC,OAAe,EAAE,MAAsB,EAAE,OAA6B;QACxE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QAC9B,OACE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,UAAU,mBAAmB,OAAO,EAAE,EAAE,OAAO,CAG9E,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;CACF;AAED,MAAM,OAAO,6BAA8B,SAAQ,qBAA8B;CAAG;AA+PpF,MAAM,CAAC,6BAA6B,GAAG,6BAA6B,CAAC;AACrE,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC"}