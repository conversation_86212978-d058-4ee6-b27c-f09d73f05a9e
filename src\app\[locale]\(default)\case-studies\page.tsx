import { getTranslations, setRequestLocale } from 'next-intl/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Link } from '@/i18n/navigation'
import { Metadata } from 'next'
import { cms, initializeCMS } from '@/cms'
import { ContentCoverImage, PageMetaTags } from '@/components/content'
import type { CaseStudyContent } from '@/cms/types'

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  setRequestLocale(locale)

  const t = await getTranslations()

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/case-studies`

  if (locale !== 'en') {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/case-studies`
  }

  return {
    title: t('case_studies.title') || 'Case Studies',
    description: t('case_studies.description') || 'Explore our case studies',
    alternates: {
      canonical: canonicalUrl,
    },
  }
}

export default async function CaseStudiesPage({
  params,
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  setRequestLocale(locale)

  const t = await getTranslations()

  // Initialize CMS and get case studies for current locale
  await initializeCMS()
  const caseStudies = await cms.getContentList<CaseStudyContent>('case-study', locale, {
    sortBy: 'publishedAt',
    order: 'desc'
  })

  return (
    <section className="py-16">
      <div className="container">
        <div className="mb-12 text-center">
          <h1 className="mb-4 text-4xl font-bold tracking-tight">
            {t('case_studies.title') || 'Case Studies'}
          </h1>
          <p className="text-xl text-muted-foreground">
            {t('case_studies.description') || 'Explore our case studies'}
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {caseStudies.map((caseStudy) => (
            <Link key={caseStudy.slug} href={`/case-studies/${caseStudy.slug}`}>
              <Card className="h-full transition-colors hover:bg-muted/50">
                {caseStudy.coverImage && (
                  <div className="aspect-video overflow-hidden rounded-t-lg">
                    <ContentCoverImage
                      image={caseStudy.coverImage}
                      title={caseStudy.title}
                      className="h-full w-full object-cover"
                    />
                  </div>
                )}
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="line-clamp-2">{caseStudy.title}</CardTitle>
                    {caseStudy.featured && (
                      <Badge variant="secondary">Featured</Badge>
                    )}
                  </div>
                  {caseStudy.description && (
                    <CardDescription className="line-clamp-3">
                      {caseStudy.description}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  {caseStudy.tags && caseStudy.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {caseStudy.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {caseStudies.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              {t('case_studies.no_case_studies') || 'No case studies found.'}
            </p>
          </div>
        )}
      </div>
    </section>
  )
}
