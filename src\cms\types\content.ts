/**
 * Content Type Definitions
 * 
 * Unified content type interfaces that abstract away specific CMS implementations.
 * These types ensure consistency across different content providers.
 */

// Base content interface that all content types must implement
export interface BaseContent {
  slug: string
  title: string
  description: string
  lang: string
  url: string
  publishedAt?: string
  updatedAt?: string
  featured?: boolean
  tags?: string[]
}

// Blog content interface
export interface BlogContent extends BaseContent {
  content: string
  excerpt?: string
  author?: string
  authorImage?: string
  coverImage?: ImageMetadata
  metadata?: ContentMetadata
}

// Product content interface
export interface ProductContent extends BaseContent {
  content: string
  price?: string
  category?: string
  videoDuration?: string
  coverImage?: ImageMetadata
}

// Case study content interface
export interface CaseStudyContent extends BaseContent {
  content: string
  client?: string
  industry?: string
  results?: string[]
  coverImage?: ImageMetadata
}

// Image metadata interface
export interface ImageMetadata {
  src: string
  width: number
  height: number
  blurDataURL?: string
  alt?: string
}

// Content metadata interface
export interface ContentMetadata {
  readingTime?: number
  words?: number
  characters?: number
}

// Content query options
export interface ContentQueryOptions {
  featured?: boolean
  limit?: number
  sortBy?: 'publishedAt' | 'updatedAt' | 'title'
  order?: 'asc' | 'desc'
  tags?: string[]
}

// Content type union
export type ContentItem = BlogContent | ProductContent | CaseStudyContent

// Content type names
export type ContentType = 'blog' | 'product' | 'case-study'

// Language switching result
export interface LanguageSwitchResult {
  url: string
  strategy: 'direct' | 'fallback-list' | 'fallback-home'
  reason?: string
}

// Content availability info
export interface ContentAvailability {
  lang: string
  title: string
  url: string
  available: boolean
}
