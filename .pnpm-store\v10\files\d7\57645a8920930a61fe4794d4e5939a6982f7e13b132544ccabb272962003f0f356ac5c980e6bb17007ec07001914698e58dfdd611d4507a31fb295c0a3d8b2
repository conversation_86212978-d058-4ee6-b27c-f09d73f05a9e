// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import * as BytimesAPI from "./bytimes.mjs";
import { Bytimes } from "./bytimes.mjs";
import * as SummariesAPI from "./summaries.mjs";
import { Summaries } from "./summaries.mjs";
export class Events extends APIResource {
    constructor() {
        super(...arguments);
        this.bytimes = new BytimesAPI.Bytimes(this._client);
        this.summaries = new SummariesAPI.Summaries(this._client);
    }
}
Events.Bytimes = Bytimes;
Events.Summaries = Summaries;
//# sourceMappingURL=events.mjs.map