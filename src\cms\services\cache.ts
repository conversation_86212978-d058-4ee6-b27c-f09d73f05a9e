/**
 * Cache Service
 * 
 * Caching layer for CMS content to improve performance and reduce
 * API calls to external CMS providers.
 */

import type { ContentItem, ContentType } from '../types'

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

interface CacheConfig {
  defaultTTL: number // Time to live in milliseconds
  maxSize: number    // Maximum number of entries
  enabled: boolean
}

export class CacheService {
  private cache = new Map<string, CacheEntry<any>>()
  private config: CacheConfig
  
  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      defaultTTL: 5 * 60 * 1000, // 5 minutes default
      maxSize: 1000,
      enabled: true,
      ...config
    }
  }
  
  /**
   * Generate cache key for content
   */
  private generateKey(type: ContentType, slug: string, locale: string): string {
    return `content:${type}:${slug}:${locale}`
  }
  
  /**
   * Generate cache key for content list
   */
  private generateListKey(type: ContentType, locale: string, options?: any): string {
    const optionsKey = options ? JSON.stringify(options) : 'default'
    return `list:${type}:${locale}:${optionsKey}`
  }
  
  /**
   * Check if cache entry is valid
   */
  private isValid<T>(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp < entry.ttl
  }
  
  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp >= entry.ttl) {
        this.cache.delete(key)
      }
    }
  }
  
  /**
   * Ensure cache doesn't exceed max size
   */
  private enforceMaxSize(): void {
    if (this.cache.size > this.config.maxSize) {
      // Remove oldest entries
      const entries = Array.from(this.cache.entries())
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
      
      const toRemove = entries.slice(0, this.cache.size - this.config.maxSize)
      toRemove.forEach(([key]) => this.cache.delete(key))
    }
  }
  
  /**
   * Get cached content
   */
  get<T extends ContentItem>(type: ContentType, slug: string, locale: string): T | null {
    if (!this.config.enabled) return null
    
    const key = this.generateKey(type, slug, locale)
    const entry = this.cache.get(key)
    
    if (entry && this.isValid(entry)) {
      return entry.data
    }
    
    // Remove expired entry
    if (entry) {
      this.cache.delete(key)
    }
    
    return null
  }
  
  /**
   * Set cached content
   */
  set<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string,
    data: T,
    ttl?: number
  ): void {
    if (!this.config.enabled) return
    
    const key = this.generateKey(type, slug, locale)
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.defaultTTL
    }
    
    this.cache.set(key, entry)
    this.enforceMaxSize()
  }
  
  /**
   * Get cached content list
   */
  getList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: any
  ): T[] | null {
    if (!this.config.enabled) return null
    
    const key = this.generateListKey(type, locale, options)
    const entry = this.cache.get(key)
    
    if (entry && this.isValid(entry)) {
      return entry.data
    }
    
    // Remove expired entry
    if (entry) {
      this.cache.delete(key)
    }
    
    return null
  }
  
  /**
   * Set cached content list
   */
  setList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    data: T[],
    options?: any,
    ttl?: number
  ): void {
    if (!this.config.enabled) return
    
    const key = this.generateListKey(type, locale, options)
    const entry: CacheEntry<T[]> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.defaultTTL
    }
    
    this.cache.set(key, entry)
    this.enforceMaxSize()
  }
  
  /**
   * Invalidate cache for specific content
   */
  invalidate(type: ContentType, slug?: string, locale?: string): void {
    if (slug && locale) {
      // Invalidate specific content
      const key = this.generateKey(type, slug, locale)
      this.cache.delete(key)
    } else {
      // Invalidate all content of this type
      const pattern = `content:${type}:`
      for (const key of this.cache.keys()) {
        if (key.startsWith(pattern)) {
          this.cache.delete(key)
        }
      }
    }
    
    // Also invalidate related lists
    const listPattern = `list:${type}:`
    for (const key of this.cache.keys()) {
      if (key.startsWith(listPattern)) {
        this.cache.delete(key)
      }
    }
  }
  
  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear()
  }
  
  /**
   * Get cache statistics
   */
  getStats(): {
    size: number
    maxSize: number
    hitRate: number
    enabled: boolean
  } {
    // Note: Hit rate tracking would require additional implementation
    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate: 0, // TODO: Implement hit rate tracking
      enabled: this.config.enabled
    }
  }
  
  /**
   * Perform cache maintenance
   */
  maintain(): void {
    this.cleanup()
    this.enforceMaxSize()
  }
}
