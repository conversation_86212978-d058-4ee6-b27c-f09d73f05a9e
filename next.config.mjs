import bundleAnalyzer from "@next/bundle-analyzer";
import createNextIntlPlugin from "next-intl/plugin";
import { createMDX } from "fumadocs-mdx/next";
import { withContentCollections } from "@content-collections/next";

const withMDX = createMDX();

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
});

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  reactStrictMode: false,
  pageExtensions: ["ts", "tsx", "js", "jsx", "md", "mdx"],
  images: {
    unoptimized: true,
  },
  async redirects() {
    return [];
  },
};

// Make sure experimental mdx flag is enabled
const configWithMDX = {
  ...nextConfig,
  experimental: {
    mdxRs: true,
  },
  // Temporary: Use unoptimized images due to plugin conflicts
  // TODO: Investigate and fix plugin conflicts with image configuration
  images: {
    unoptimized: true,
  },
};

const finalConfig = withBundleAnalyzer(withNextIntl(withMDX(withContentCollections(configWithMDX))));

// Force image configuration as final step to override any plugin modifications
finalConfig.images = {
  unoptimized: true,
};

export default finalConfig;

import { initOpenNextCloudflareForDev } from "@opennextjs/cloudflare";
initOpenNextCloudflareForDev();
