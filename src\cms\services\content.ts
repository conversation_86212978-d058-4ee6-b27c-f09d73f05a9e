/**
 * Content Service
 * 
 * High-level content management service that provides a unified API
 * for content operations across different CMS providers.
 */

import type {
  CMSProvider,
  ContentItem,
  ContentType,
  ContentQueryOptions,
  ContentAvailability
} from '../types'

export class ContentService {
  private provider: CMSProvider
  
  constructor(provider: CMSProvider) {
    this.provider = provider
  }
  
  /**
   * Get a single content item by type, slug, and locale
   */
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    try {
      return await this.provider.getContent<T>(type, slug, locale)
    } catch (error) {
      console.error(`Error fetching content ${type}/${slug}/${locale}:`, error)
      return null
    }
  }
  
  /**
   * Get a list of content items by type and locale
   */
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: ContentQueryOptions
  ): Promise<T[]> {
    try {
      return await this.provider.getContentList<T>(type, locale, options)
    } catch (error) {
      console.error(`Error fetching content list ${type}/${locale}:`, error)
      return []
    }
  }
  
  /**
   * Check if content exists in a specific locale
   */
  async contentExists(type: ContentType, slug: string, locale: string): Promise<boolean> {
    try {
      return await this.provider.contentExists(type, slug, locale)
    } catch (error) {
      console.error(`Error checking content existence ${type}/${slug}/${locale}:`, error)
      return false
    }
  }
  
  /**
   * Get content title without fetching full content
   */
  async getContentTitle(type: ContentType, slug: string, locale: string): Promise<string | null> {
    try {
      return await this.provider.getContentTitle(type, slug, locale)
    } catch (error) {
      console.error(`Error fetching content title ${type}/${slug}/${locale}:`, error)
      return null
    }
  }
  
  /**
   * Get available language versions of content
   */
  async getAvailableLanguageVersions(
    type: ContentType,
    slug: string
  ): Promise<ContentAvailability[]> {
    try {
      return await this.provider.getAvailableLanguageVersions(type, slug)
    } catch (error) {
      console.error(`Error fetching language versions ${type}/${slug}:`, error)
      return []
    }
  }
  
  /**
   * Get related content items
   */
  async getRelatedContent<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit: number = 3
  ): Promise<T[]> {
    try {
      return await this.provider.getRelatedContent<T>(type, currentSlug, locale, limit)
    } catch (error) {
      console.error(`Error fetching related content ${type}/${currentSlug}/${locale}:`, error)
      return []
    }
  }
  
  /**
   * Get featured content items
   */
  async getFeaturedContent<T extends ContentItem>(
    type: ContentType,
    locale: string,
    limit: number = 5
  ): Promise<T[]> {
    return this.getContentList<T>(type, locale, {
      featured: true,
      limit,
      sortBy: 'publishedAt',
      order: 'desc'
    })
  }
  
  /**
   * Get recent content items
   */
  async getRecentContent<T extends ContentItem>(
    type: ContentType,
    locale: string,
    limit: number = 10
  ): Promise<T[]> {
    return this.getContentList<T>(type, locale, {
      limit,
      sortBy: 'publishedAt',
      order: 'desc'
    })
  }
  
  /**
   * Search content by tags
   */
  async getContentByTags<T extends ContentItem>(
    type: ContentType,
    locale: string,
    tags: string[],
    limit?: number
  ): Promise<T[]> {
    return this.getContentList<T>(type, locale, {
      tags,
      limit,
      sortBy: 'publishedAt',
      order: 'desc'
    })
  }
  
  /**
   * Get content statistics
   */
  async getContentStats(locale: string): Promise<{
    blogs: number
    products: number
    caseStudies: number
    total: number
  }> {
    try {
      const [blogs, products, caseStudies] = await Promise.all([
        this.getContentList('blog', locale),
        this.getContentList('product', locale),
        this.getContentList('case-study', locale)
      ])
      
      return {
        blogs: blogs.length,
        products: products.length,
        caseStudies: caseStudies.length,
        total: blogs.length + products.length + caseStudies.length
      }
    } catch (error) {
      console.error('Error fetching content stats:', error)
      return { blogs: 0, products: 0, caseStudies: 0, total: 0 }
    }
  }
}
