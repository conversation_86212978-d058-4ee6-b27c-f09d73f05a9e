// content-collections.ts
import { defineCollection, defineConfig } from "@content-collections/core";
import { compileMDX } from "@content-collections/mdx";
import { z } from "zod";
var contentSchema = z.object({
  title: z.string(),
  slug: z.string(),
  description: z.string().optional(),
  coverImage: z.string().optional(),
  author: z.string().optional(),
  authorImage: z.string().optional(),
  publishedAt: z.coerce.date().optional(),
  featured: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  videoUrl: z.string().optional(),
  videoThumbnail: z.string().optional(),
  videoDuration: z.string().optional()
});
var blogs = defineCollection({
  name: "blogs",
  directory: "content/blogs",
  include: "**/*.mdx",
  schema: contentSchema,
  transform: async (document, context) => {
    const mdx = await compileMDX(context, document);
    const pathParts = document._meta.path.split("/");
    const lang = pathParts[0];
    const url = `/${lang}/blogs/${document.slug}`;
    return {
      ...document,
      lang,
      url,
      mdx,
      publishedAt: document.publishedAt || /* @__PURE__ */ new Date()
    };
  }
});
var products = defineCollection({
  name: "products",
  directory: "content/products",
  include: "**/*.mdx",
  schema: contentSchema,
  transform: async (document, context) => {
    const mdx = await compileMDX(context, document);
    const pathParts = document._meta.path.split("/");
    const lang = pathParts[0];
    const url = `/${lang}/products/${document.slug}`;
    return {
      ...document,
      lang,
      url,
      mdx,
      publishedAt: document.publishedAt || /* @__PURE__ */ new Date()
    };
  }
});
var caseStudies = defineCollection({
  name: "caseStudies",
  directory: "content/case-studies",
  include: "**/*.mdx",
  schema: contentSchema,
  transform: async (document, context) => {
    const mdx = await compileMDX(context, document);
    const pathParts = document._meta.path.split("/");
    const lang = pathParts[0];
    const url = `/${lang}/case-studies/${document.slug}`;
    return {
      ...document,
      lang,
      url,
      mdx,
      publishedAt: document.publishedAt || /* @__PURE__ */ new Date()
    };
  }
});
var content_collections_default = defineConfig({
  collections: [blogs, products, caseStudies]
});
export {
  content_collections_default as default
};
