// content-collections.ts
import { defineCollection, defineConfig } from "@content-collections/core";
import { compileMDX } from "@content-collections/mdx";
import { z } from "zod";
import fs from "fs";
import path from "path";
var commonSchema = z.object({
  // Required Fields
  title: z.string(),
  // Content title for display
  slug: z.string(),
  // URL slug for routing
  // SEO and Metadata Fields
  description: z.string().optional(),
  // Meta description for SEO
  coverImage: z.string().optional(),
  // Hero/cover image URL
  // Author Information
  author: z.string().optional(),
  // Author name
  authorImage: z.string().optional(),
  // Author avatar URL
  // Publishing and Organization
  publishedAt: z.date().optional(),
  // Publication date
  featured: z.boolean().default(false),
  // Featured content flag
  tags: z.array(z.string()).default([]),
  // Content tags/categories
  // Video Content Support
  videoUrl: z.string().optional(),
  // Self-hosted video URL
  videoThumbnail: z.string().optional(),
  // Video thumbnail/poster image
  videoDuration: z.string().optional()
  // Video duration (e.g., "5:30")
});
var createContentCollection = (name, directory) => {
  return defineCollection({
    name,
    // Directory to scan for content files
    directory,
    // Include all MDX files in subdirectories
    include: "**/*.mdx",
    // Schema validation using Zod
    schema: commonSchema,
    // Transform function to process content and add computed fields
    transform: async (document, context) => {
      const mdx = await compileMDX(context, document);
      const pathParts = document._meta.path.split("/");
      const lang = pathParts[pathParts.length - 2];
      const contentType = directory.split("/")[1];
      const url = `/${lang}/${contentType}/${document.slug}`;
      let createdAt;
      try {
        const filePath = path.join(process.cwd(), directory, document._meta.path);
        const stats = fs.statSync(filePath);
        createdAt = stats.birthtime;
      } catch (error) {
        createdAt = /* @__PURE__ */ new Date();
      }
      return {
        ...document,
        // Computed fields
        lang,
        // Language code extracted from path
        url,
        // Full URL path for the content
        mdx,
        // Compiled MDX for rendering
        createdAt,
        // File creation timestamp
        // Use publishedAt if provided, otherwise fall back to createdAt
        publishedAt: document.publishedAt || createdAt
      };
    }
  });
};
var blogs = createContentCollection("blogs", "content/blogs");
var products = createContentCollection("products", "content/products");
var caseStudies = createContentCollection("caseStudies", "content/case-studies");
var content_collections_default = defineConfig({
  collections: [blogs, products, caseStudies]
});
export {
  content_collections_default as default
};
