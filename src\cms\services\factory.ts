/**
 * Service Factory
 * 
 * Factory for creating and managing CMS service instances.
 * Provides a centralized way to configure and access services.
 */

import { ContentService } from './content'
import { CacheService } from './cache'
import { SEOService } from './seo'
import type { CMSProvider } from '../types'

interface ServiceFactoryConfig {
  baseUrl: string
  siteName: string
  siteDescription: string
  defaultLocale: string
  locales: string[]
  cache: {
    enabled: boolean
    defaultTTL: number
    maxSize: number
  }
  social: {
    twitter?: string
    facebook?: string
  }
}

export class ServiceFactory {
  private config: ServiceFactoryConfig
  private contentService?: ContentService
  private cacheService?: CacheService
  private seoService?: SEOService
  
  constructor(config: ServiceFactoryConfig) {
    this.config = config
  }
  
  /**
   * Create or get content service instance
   */
  getContentService(provider: CMSProvider): ContentService {
    if (!this.contentService) {
      this.contentService = new ContentService(provider)
    }
    return this.contentService
  }
  
  /**
   * Create or get cache service instance
   */
  getCacheService(): CacheService {
    if (!this.cacheService) {
      this.cacheService = new CacheService(this.config.cache)
    }
    return this.cacheService
  }
  
  /**
   * Create or get SEO service instance
   */
  getSEOService(): SEOService {
    if (!this.seoService) {
      this.seoService = new SEOService({
        baseUrl: this.config.baseUrl,
        siteName: this.config.siteName,
        siteDescription: this.config.siteDescription,
        defaultLocale: this.config.defaultLocale,
        locales: this.config.locales,
        social: this.config.social
      })
    }
    return this.seoService
  }
  
  /**
   * Reset all service instances (useful for testing)
   */
  reset(): void {
    this.contentService = undefined
    this.cacheService = undefined
    this.seoService = undefined
  }
  
  /**
   * Update configuration
   */
  updateConfig(config: Partial<ServiceFactoryConfig>): void {
    this.config = { ...this.config, ...config }
    // Reset services to pick up new config
    this.reset()
  }
}
