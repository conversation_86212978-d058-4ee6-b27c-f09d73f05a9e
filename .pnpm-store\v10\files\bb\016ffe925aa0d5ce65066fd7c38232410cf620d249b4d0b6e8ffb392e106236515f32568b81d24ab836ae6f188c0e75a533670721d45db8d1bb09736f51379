{"version": 3, "file": "applications.js", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/access/applications/applications.ts"], "names": [], "mappings": ";AAAA,sFAAsF;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtF,sDAAmD;AACnD,8CAAoD;AAIpD,iDAAgC;AAChC,kCASe;AACf,uEAAsD;AACtD,4CAiCoB;AACpB,2DAA0C;AAC1C,4CAMoB;AACpB,6EAA4D;AAC5D,gEAK8B;AAC9B,+EAA8D;AAC9D,iEAMqC;AACrC,gDAAoD;AACpD,0DAAoD;AAEpD,MAAa,YAAa,SAAQ,sBAAW;IAA7C;;QACE,QAAG,GAAe,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,qBAAgB,GAAyC,IAAI,mBAAmB,CAAC,gBAAgB,CAC/F,IAAI,CAAC,OAAO,CACb,CAAC;QACF,aAAQ,GAAqC,IAAI,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChG,gBAAW,GAA+B,IAAI,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvF,aAAQ,GAAyB,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAwS1E,CAAC;IAtSC;;;;;;;;;;;;OAYG;IACH,MAAM,CACJ,MAA+B,EAC/B,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QAChD,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,uBAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,uBAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OACE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,aAAa,IAAI,eAAe,cAAc,EAAE;YACpE,IAAI;YACJ,GAAG,OAAO;SACX,CACF,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,MAAM,CACJ,KAAiB,EACjB,MAA+B,EAC/B,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QAChD,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,uBAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,uBAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OACE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,aAAa,IAAI,eAAe,gBAAgB,KAAK,EAAE,EAAE;YAC5E,IAAI;YACJ,GAAG,OAAO;SACX,CACF,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAsBD,IAAI,CACF,SAAsD,EAAE,EACxD,OAA6B;QAE7B,IAAI,IAAA,uBAAgB,EAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;SAC9B;QACD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,CAAC;QACjD,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,uBAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,uBAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAC5B,IAAI,aAAa,IAAI,eAAe,cAAc,EAClD,kCAAkC,EAClC,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,CACtB,CAAC;IACJ,CAAC;IAoBD,MAAM,CACJ,KAAiB,EACjB,SAAwD,EAAE,EAC1D,OAA6B;QAE7B,IAAI,IAAA,uBAAgB,EAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SACvC;QACD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QACvC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,uBAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,uBAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OACE,IAAI,CAAC,OAAO,CAAC,MAAM,CACjB,IAAI,aAAa,IAAI,eAAe,gBAAgB,KAAK,EAAE,EAC3D,OAAO,CAEV,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAoBD,GAAG,CACD,KAAiB,EACjB,SAAqD,EAAE,EACvD,OAA6B;QAE7B,IAAI,IAAA,uBAAgB,EAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SACpC;QACD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QACvC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,uBAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,uBAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OACE,IAAI,CAAC,OAAO,CAAC,GAAG,CACd,IAAI,aAAa,IAAI,eAAe,gBAAgB,KAAK,EAAE,EAC3D,OAAO,CAEV,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAuBD,YAAY,CACV,KAAiB,EACjB,SAA8D,EAAE,EAChE,OAA6B;QAE7B,IAAI,IAAA,uBAAgB,EAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;SAC7C;QACD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QACvC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YAC3B,MAAM,IAAI,uBAAe,CAAC,gDAAgD,CAAC,CAAC;SAC7E;QACD,IAAI,UAAU,IAAI,OAAO,EAAE;YACzB,MAAM,IAAI,uBAAe,CAAC,iDAAiD,CAAC,CAAC;SAC9E;QACD,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GACtC,UAAU,CAAC,CAAC;YACV;gBACE,aAAa,EAAE,UAAU;gBACzB,eAAe,EAAE,UAAU;aAC5B;YACH,CAAC,CAAC;gBACE,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,OAAO;aACzB,CAAC;QACN,OACE,IAAI,CAAC,OAAO,CAAC,IAAI,CACf,IAAI,aAAa,IAAI,eAAe,gBAAgB,KAAK,gBAAgB,EACzE,OAAO,CAEV,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;CACF;AA/SD,oCA+SC;AAED,MAAa,kCAAmC,SAAQ,uBAAmC;CAAG;AAA9F,gFAA8F;AAolqB9F,YAAY,CAAC,kCAAkC,GAAG,kCAAkC,CAAC;AACrF,YAAY,CAAC,GAAG,GAAG,SAAG,CAAC;AACvB,YAAY,CAAC,aAAa,GAAG,mBAAa,CAAC;AAC3C,YAAY,CAAC,gBAAgB,GAAG,qCAAgB,CAAC;AACjD,YAAY,CAAC,QAAQ,GAAG,mBAAQ,CAAC;AACjC,YAAY,CAAC,6BAA6B,GAAG,wCAA6B,CAAC;AAC3E,YAAY,CAAC,WAAW,GAAG,0BAAW,CAAC;AACvC,YAAY,CAAC,QAAQ,GAAG,mBAAQ,CAAC"}