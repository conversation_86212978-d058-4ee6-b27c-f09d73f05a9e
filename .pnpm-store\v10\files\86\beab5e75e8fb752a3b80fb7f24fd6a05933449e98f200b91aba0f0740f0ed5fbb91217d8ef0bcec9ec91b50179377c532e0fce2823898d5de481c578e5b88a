import { APIResource } from "../../../../resource.js";
import * as TargetsAPI from "./targets.js";
import { TargetBulkDeleteParams, TargetBulkDeleteV2Params, TargetBulkUpdateParams, TargetBulkUpdateResponse, TargetBulkUpdateResponsesSinglePage, TargetCreateParams, TargetCreateResponse, TargetDeleteParams, TargetGetParams, TargetGetResponse, TargetListParams, TargetListResponse, TargetListResponsesV4PagePaginationArray, TargetUpdateParams, TargetUpdateResponse, Targets } from "./targets.js";
export declare class Infrastructure extends APIResource {
    targets: TargetsAPI.Targets;
}
export declare namespace Infrastructure {
    export { Targets as Targets, type TargetCreateResponse as TargetCreateResponse, type TargetUpdateResponse as TargetUpdateResponse, type TargetListResponse as TargetListResponse, type TargetBulkUpdateResponse as TargetBulkUpdateResponse, type TargetGetResponse as TargetGetResponse, TargetListResponsesV4PagePaginationArray as TargetListResponsesV4PagePaginationArray, TargetBulkUpdateResponsesSinglePage as TargetBulkUpdateResponsesSinglePage, type TargetCreateParams as TargetCreateParams, type TargetUpdateParams as TargetUpdateParams, type TargetListParams as TargetListParams, type TargetDeleteParams as TargetDeleteParams, type TargetBulkDeleteParams as TargetBulkDeleteParams, type TargetBulkDeleteV2Params as TargetBulkDeleteV2Params, type TargetBulkUpdateParams as TargetBulkUpdateParams, type TargetGetParams as TargetGetParams, };
}
//# sourceMappingURL=infrastructure.d.ts.map