{"version": 3, "file": "namespaces.d.ts", "sourceRoot": "", "sources": ["../../../../src/resources/workers-for-platforms/dispatch/namespaces/namespaces.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AACzC,OAAO,KAAK,UAAU,MAAM,mBAAmB,CAAC;AAChD,OAAO,EACL,MAAM,EACN,kBAAkB,EAClB,oBAAoB,EACpB,eAAe,EACf,kBAAkB,EAClB,oBAAoB,EACpB,OAAO,EACR,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AAEpD,qBAAa,UAAW,SAAQ,WAAW;IACzC,OAAO,EAAE,UAAU,CAAC,OAAO,CAAwC;IAEnE;;;;;;;;;;OAUG;IACH,MAAM,CACJ,MAAM,EAAE,qBAAqB,EAC7B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC;IAU3C;;;;;;;;;;;;OAYG;IACH,IAAI,CACF,MAAM,EAAE,mBAAmB,EAC3B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,WAAW,CAAC,gCAAgC,EAAE,qBAAqB,CAAC;IAS5E;;;;;;;;;;;OAWG;IACH,MAAM,CACJ,iBAAiB,EAAE,MAAM,EACzB,MAAM,EAAE,qBAAqB,EAC7B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,uBAAuB,GAAG,IAAI,CAAC;IAUlD;;;;;;;;;;;OAWG;IACH,GAAG,CACD,iBAAiB,EAAE,MAAM,EACzB,MAAM,EAAE,kBAAkB,EAC1B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC;CASzC;AAED,qBAAa,gCAAiC,SAAQ,UAAU,CAAC,qBAAqB,CAAC;CAAG;AAE1F,MAAM,WAAW,uBAAuB;IACtC;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,WAAW,qBAAqB;IACpC;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,MAAM,uBAAuB,GAAG,OAAO,CAAC;AAE9C,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,WAAW,qBAAqB;IACpC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,qBAAqB;IACpC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;CACpB;AAKD,MAAM,CAAC,OAAO,WAAW,UAAU,CAAC;IAClC,OAAO,EACL,KAAK,uBAAuB,IAAI,uBAAuB,EACvD,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,uBAAuB,IAAI,uBAAuB,EACvD,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,gCAAgC,IAAI,gCAAgC,EACpE,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,kBAAkB,IAAI,kBAAkB,GAC9C,CAAC;IAEF,OAAO,EACL,OAAO,IAAI,OAAO,EAClB,KAAK,MAAM,IAAI,MAAM,EACrB,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,eAAe,IAAI,eAAe,GACxC,CAAC;CACH"}