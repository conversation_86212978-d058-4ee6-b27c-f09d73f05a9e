/**
 * Base CMS Provider
 * 
 * Abstract base class that defines the common interface for all CMS providers.
 * This ensures consistency across different CMS implementations while allowing
 * for provider-specific optimizations.
 */

import type {
  CMSProvider,
  ContentItem,
  ContentType,
  ContentQueryOptions,
  ContentAvailability,
  ProviderInitOptions
} from '../types'

export abstract class BaseCMSProvider implements CMSProvider {
  // Provider identification
  abstract readonly name: string
  abstract readonly version: string
  
  // Initialization state
  protected initialized = false
  protected config: any = {}
  
  constructor(config?: any) {
    this.config = config || {}
  }
  
  // Abstract methods that must be implemented by concrete providers
  abstract getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null>
  
  abstract getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: ContentQueryOptions
  ): Promise<T[]>
  
  // Default implementations that can be overridden
  async contentExists(type: ContentType, slug: string, locale: string): Promise<boolean> {
    const content = await this.getContent(type, slug, locale)
    return content !== null
  }
  
  async getContentTitle(type: ContentType, slug: string, locale: string): Promise<string | null> {
    const content = await this.getContent(type, slug, locale)
    return content?.title || null
  }
  
  async getAvailableLanguageVersions(
    type: ContentType,
    slug: string
  ): Promise<ContentAvailability[]> {
    // Default implementation - should be overridden by providers for better performance
    const locales = ['en', 'zh'] // TODO: Make this configurable
    const versions: ContentAvailability[] = []
    
    for (const locale of locales) {
      const content = await this.getContent(type, slug, locale)
      if (content) {
        versions.push({
          lang: locale,
          title: content.title,
          url: content.url,
          available: true
        })
      }
    }
    
    return versions
  }
  
  async getRelatedContent<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit: number = 3
  ): Promise<T[]> {
    // Default implementation - get recent content excluding current
    const allContent = await this.getContentList<T>(type, locale, {
      sortBy: 'publishedAt',
      order: 'desc'
    })
    
    return allContent
      .filter(item => item.slug !== currentSlug)
      .slice(0, limit)
  }
  
  // Provider lifecycle methods
  async initialize(options?: ProviderInitOptions): Promise<void> {
    if (this.initialized) {
      return
    }
    
    // Perform provider-specific initialization
    await this.doInitialize(options)
    this.initialized = true
  }
  
  async destroy(): Promise<void> {
    if (!this.initialized) {
      return
    }
    
    // Perform provider-specific cleanup
    await this.doDestroy()
    this.initialized = false
  }
  
  // Protected methods for subclasses to override
  protected async doInitialize(options?: ProviderInitOptions): Promise<void> {
    // Default implementation - no-op
  }
  
  protected async doDestroy(): Promise<void> {
    // Default implementation - no-op
  }
  
  // Utility methods for subclasses
  protected validateContentType(type: ContentType): void {
    const validTypes: ContentType[] = ['blog', 'product', 'case-study']
    if (!validTypes.includes(type)) {
      throw new Error(`Invalid content type: ${type}. Valid types are: ${validTypes.join(', ')}`)
    }
  }
  
  protected validateLocale(locale: string): void {
    const validLocales = ['en', 'zh'] // TODO: Make this configurable
    if (!validLocales.includes(locale)) {
      throw new Error(`Invalid locale: ${locale}. Valid locales are: ${validLocales.join(', ')}`)
    }
  }
  
  protected normalizeSlug(slug: string): string {
    return slug.toLowerCase().trim()
  }
  
  // Helper method to check if provider is ready
  protected ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error(`Provider ${this.name} is not initialized. Call initialize() first.`)
    }
  }
}
