# CMS Usage Guide

## 🎯 Overview

This guide explains how to use the new CMS abstraction layer for content management, SEO optimization, and component development.

## 🚀 Quick Start

### Basic Setup
```typescript
import { cms, initializeCMS } from '@/cms'
import type { BlogContent, ProductContent, CaseStudyContent } from '@/cms/types'

// Initialize CMS (usually done once per request/page)
await initializeCMS()

// Now you can use the CMS
const blogs = await cms.getContentList<BlogContent>('blog', 'en')
```

### Page Component Example
```typescript
export default async function BlogPage({ params }: { params: { locale: string, slug: string } }) {
  const { locale, slug } = params
  
  // Initialize CMS
  await initializeCMS()
  
  // Get content
  const blog = await cms.getContent<BlogContent>('blog', slug, locale)
  
  if (!blog) {
    notFound()
  }
  
  // Get related content
  const related = await cms.getRelatedContent<BlogContent>('blog', slug, locale, 3)
  
  return (
    <>
      {/* SEO Components */}
      <ContentStructuredData content={blog} />
      <ContentMetaTags content={blog} />
      
      {/* Content */}
      <article>
        <h1>{blog.title}</h1>
        <ContentCoverImage image={blog.coverImage} title={blog.title} />
        <div dangerouslySetInnerHTML={{ __html: blog.content }} />
      </article>
      
      {/* Related Content */}
      {related.length > 0 && (
        <section>
          <h2>Related Posts</h2>
          {related.map(post => (
            <div key={post.slug}>{post.title}</div>
          ))}
        </section>
      )}
    </>
  )
}
```

## 📚 Core APIs

### Content Operations

#### Get Single Content
```typescript
const blog = await cms.getContent<BlogContent>('blog', 'my-post', 'en')
const product = await cms.getContent<ProductContent>('product', 'my-product', 'zh')
const caseStudy = await cms.getContent<CaseStudyContent>('case-study', 'my-case', 'en')
```

#### Get Content List
```typescript
// Get all blogs in English
const blogs = await cms.getContentList<BlogContent>('blog', 'en')

// Get featured products
const featured = await cms.getContentList<ProductContent>('product', 'en', {
  featured: true,
  limit: 5
})

// Get recent case studies
const recent = await cms.getContentList<CaseStudyContent>('case-study', 'zh', {
  sortBy: 'publishedAt',
  order: 'desc',
  limit: 10
})

// Get content by tags
const tagged = await cms.getContentList<BlogContent>('blog', 'en', {
  tags: ['AI', 'SaaS'],
  limit: 20
})
```

#### Content Utilities
```typescript
// Check if content exists
const exists = await cms.contentExists('blog', 'my-post', 'en')

// Get content title only
const title = await cms.getContentTitle('blog', 'my-post', 'en')

// Get available language versions
const versions = await cms.getAvailableLanguageVersions('blog', 'my-post')
// Returns: [{ lang: 'en', title: '...', url: '...', available: true }, ...]

// Get related content
const related = await cms.getRelatedContent<BlogContent>('blog', 'current-post', 'en', 3)

// Get featured content
const featured = await cms.getFeaturedContent<BlogContent>('blog', 'en', 5)

// Get recent content
const recent = await cms.getRecentContent<BlogContent>('blog', 'en', 10)
```

### SEO Services

#### Meta Tags Generation
```typescript
const seoService = cms.getSEOService()

// Generate meta tags for content
const metaTags = seoService.generateMetaTags(blog)
// Returns: { title, description, canonical, openGraph, twitter, alternates }

// Use in component
<ContentMetaTags content={blog} />

// Or manually
<PageMetaTags
  title="Custom Title"
  description="Custom description"
  url="/custom-page"
  image={{ url: '/image.jpg', width: 1200, height: 630, alt: 'Image' }}
/>
```

#### Structured Data Generation
```typescript
// Generate structured data for content
const structuredData = seoService.generateStructuredData(blog)
// Returns: BlogPosting, Product, or Article schema

// Use in component
<ContentStructuredData content={blog} />

// Or manually
<OrganizationStructuredData />
<BreadcrumbStructuredData items={[
  { name: 'Home', url: '/' },
  { name: 'Blog', url: '/blogs' },
  { name: blog.title, url: blog.url }
]} />
```

#### Sitemap and RSS Generation
```typescript
// Generate sitemap (usually in build scripts)
const sitemap = seoService.generateSitemap(allContent)

// Generate RSS feeds
const rssFeeds = seoService.generateRSSFeeds(blogs)
// Returns: { en: RSSFeed, zh: RSSFeed }
```

### Caching Services

#### Cache Management
```typescript
const cacheService = cms.getCacheService()

// Get cache statistics
const stats = cacheService.getStats()
console.log(`Cache size: ${stats.size}, Hit rate: ${stats.hitRate}%`)

// Manual cache operations
cacheService.invalidate('blog', 'my-post', 'en') // Invalidate specific content
cacheService.clear() // Clear all cache
cacheService.maintain() // Run maintenance (cleanup expired entries)
```

#### Cache Configuration
```typescript
// Cache is configured in src/cms/config.ts
export const cmsConfig = {
  cache: {
    enabled: true,
    ttl: 5 * 60 * 1000, // 5 minutes
    strategy: 'memory'
  }
}
```

## 🎨 Components

### Image Components
```typescript
import { 
  ContentImage, 
  ContentCoverImage, 
  ContentThumbnail, 
  AuthorImage,
  ResponsiveContentImage 
} from '@/components/content'

// Basic content image
<ContentImage 
  image={blog.coverImage} 
  alt={blog.title}
  priority={true}
/>

// Cover image with consistent styling
<ContentCoverImage 
  image={blog.coverImage} 
  title={blog.title}
  priority={true}
/>

// Thumbnail with size variants
<ContentThumbnail 
  image={blog.coverImage} 
  title={blog.title}
  size="lg" // sm, md, lg
/>

// Author profile image
<AuthorImage 
  image={blog.authorImage} 
  author={blog.author}
  size="md"
/>

// Responsive image with aspect ratio
<ResponsiveContentImage 
  image={blog.coverImage}
  alt={blog.title}
  aspectRatio="video" // square, video, wide, tall
/>
```

### SEO Components
```typescript
import { 
  StructuredData,
  ContentStructuredData, 
  OrganizationStructuredData,
  BreadcrumbStructuredData,
  MetaTags,
  ContentMetaTags,
  PageMetaTags 
} from '@/components/content'

// Automatic SEO for content
<ContentStructuredData content={blog} />
<ContentMetaTags content={blog} />

// Manual SEO
<OrganizationStructuredData />
<BreadcrumbStructuredData items={breadcrumbs} />
<PageMetaTags title="Page Title" description="Description" url="/page" />
```

## 🔧 Advanced Usage

### Custom Content Queries
```typescript
// Get content with custom filtering
const customBlogs = await cms.getContentList<BlogContent>('blog', 'en', {
  featured: true,
  tags: ['AI', 'Machine Learning'],
  sortBy: 'publishedAt',
  order: 'desc',
  limit: 10
})

// Chain operations
const recentFeatured = await cms.getFeaturedContent<BlogContent>('blog', 'en', 20)
const filtered = recentFeatured.filter(blog => 
  blog.tags?.includes('AI') && 
  new Date(blog.publishedAt) > new Date('2024-01-01')
)
```

### Error Handling
```typescript
try {
  await initializeCMS()
  const content = await cms.getContent('blog', slug, locale)
  
  if (!content) {
    notFound()
  }
  
  return content
} catch (error) {
  console.error('CMS Error:', error)
  // Handle error appropriately
  throw error
}
```

### Performance Optimization
```typescript
// Parallel content loading
const [blogs, products, featured] = await Promise.all([
  cms.getContentList<BlogContent>('blog', locale),
  cms.getContentList<ProductContent>('product', locale),
  cms.getFeaturedContent<BlogContent>('blog', locale, 5)
])

// Efficient related content loading
const related = await cms.getRelatedContent<BlogContent>('blog', slug, locale, 3)
```

## 🌐 Multi-language Support

### Language-aware Content
```typescript
// Get content in specific language
const enBlog = await cms.getContent<BlogContent>('blog', 'my-post', 'en')
const zhBlog = await cms.getContent<BlogContent>('blog', 'my-post', 'zh')

// Check language availability
const versions = await cms.getAvailableLanguageVersions('blog', 'my-post')
const hasZhVersion = versions.some(v => v.lang === 'zh' && v.available)

// Language switching
import { handleContentLanguageSwitch } from '@/services/content'

const switchResult = await handleContentLanguageSwitch(pathname, 'en', 'zh')
router.push(switchResult.url)

if (switchResult.strategy === 'fallback-list') {
  toast.info('Content not available in Chinese, showing list page')
}
```

## 📊 Monitoring and Debugging

### Debug Information
```typescript
// Get provider information
const providerInfo = cms.getProviderInfo()
console.log(`Using ${providerInfo?.name} v${providerInfo?.version}`)

// Get available providers
const providers = cms.getAvailableProviders()
console.log('Available providers:', providers)

// Cache statistics
const cacheStats = cms.getCacheService().getStats()
console.log('Cache stats:', cacheStats)
```

### Testing
```bash
# Test CMS functionality
pnpm test:cms

# Build and test content
pnpm build:content
pnpm generate:content
```

## 🔄 Provider Switching

### Switch to Different Provider
```typescript
// Switch to Strapi (when implemented)
await cms.switchProvider('strapi', {
  apiUrl: 'https://your-strapi.com/api',
  token: 'your-api-token'
})

// Switch to Sanity (when implemented)
await cms.switchProvider('sanity', {
  projectId: 'your-project-id',
  dataset: 'production'
})
```

## 📝 Best Practices

1. **Always initialize CMS** before using any content operations
2. **Use TypeScript types** for better development experience
3. **Handle errors gracefully** with try-catch blocks
4. **Leverage caching** for better performance
5. **Use SEO components** for better search engine optimization
6. **Optimize images** with the provided image components
7. **Test thoroughly** with the provided test scripts

## 🆘 Troubleshooting

See the [Migration Guide](./CMS_MIGRATION_GUIDE.md#troubleshooting) for common issues and solutions.
