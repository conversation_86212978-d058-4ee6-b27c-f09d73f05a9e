{"version": 3, "file": "rules.js", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/dlp/email/rules.ts"], "names": [], "mappings": ";AAAA,sFAAsF;;;AAEtF,sDAAmD;AAEnD,0DAAoD;AAEpD,MAAa,KAAM,SAAQ,sBAAW;IACpC;;;;;;;;;;;;;;;;;;;OAmBG;IACH,MAAM,CAAC,MAAwB,EAAE,OAA6B;QAC5D,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QACvC,OACE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,UAAU,kBAAkB,EAAE,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,CAGlF,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,MAAM,CACJ,MAAc,EACd,MAAwB,EACxB,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QACvC,OACE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,UAAU,oBAAoB,MAAM,EAAE,EAAE;YACpE,IAAI;YACJ,GAAG,OAAO;SACX,CACF,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,IAAI,CACF,MAAsB,EACtB,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAC5B,aAAa,UAAU,kBAAkB,EACzC,2BAA2B,EAC3B,OAAO,CACR,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACH,MAAM,CACJ,MAAc,EACd,MAAwB,EACxB,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QAC9B,OACE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,UAAU,oBAAoB,MAAM,EAAE,EAAE,OAAO,CAGjF,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;OAWG;IACH,QAAQ,CAAC,MAA0B,EAAE,OAA6B;QAChE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QACvC,OACE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,UAAU,kBAAkB,EAAE,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,CAGnF,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;OAUG;IACH,GAAG,CACD,MAAc,EACd,MAAqB,EACrB,OAA6B;QAE7B,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QAC9B,OACE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,UAAU,oBAAoB,MAAM,EAAE,EAAE,OAAO,CAG9E,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;CACF;AAhKD,sBAgKC;AAED,MAAa,2BAA4B,SAAQ,uBAA4B;CAAG;AAAhF,kEAAgF;AAoWhF,KAAK,CAAC,2BAA2B,GAAG,2BAA2B,CAAC"}