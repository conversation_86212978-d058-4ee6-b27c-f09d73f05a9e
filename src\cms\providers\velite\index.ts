/**
 * Velite Provider Module
 * 
 * Main entry point for the Velite CMS provider.
 * Exports the provider class and related utilities.
 */

// Export the main provider
export { VeliteProvider } from './provider'

// Export configuration
export { default as veliteConfig } from './config/velite.config'
export * from './config/schemas'

// Export utilities (to be implemented)
// export * from './utils'

// Default export for convenience
export { VeliteProvider as default } from './provider'
