// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import * as CurrentsAPI from "./currents.mjs";
import { Currents } from "./currents.mjs";
export class Aggregates extends APIResource {
    constructor() {
        super(...arguments);
        this.currents = new CurrentsAPI.Currents(this._client);
    }
}
Aggregates.Currents = Currents;
//# sourceMappingURL=aggregates.mjs.map