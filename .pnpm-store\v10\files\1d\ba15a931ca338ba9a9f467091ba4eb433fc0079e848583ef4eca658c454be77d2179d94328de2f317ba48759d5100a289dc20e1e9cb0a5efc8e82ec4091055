{"version": 3, "file": "custom.d.ts", "sourceRoot": "", "sources": ["../../../../src/resources/zero-trust/dlp/entries/custom.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AACzC,OAAO,KAAK,iBAAiB,MAAM,oBAAoB,CAAC;AAExD,qBAAa,MAAO,SAAQ,WAAW;IACrC;;;;;;;;;;;;;;OAcG;IACH,MAAM,CAAC,MAAM,EAAE,kBAAkB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC;IASxG;;;;;;;;;;;;;;;;OAgBG;IACH,MAAM,CACJ,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,kBAAkB,EAC1B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC;IAUxC;;;;;;;;;;;OAWG;IACH,MAAM,CACJ,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,kBAAkB,EAC1B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,oBAAoB,GAAG,IAAI,CAAC;CAQhD;AAED,MAAM,WAAW,oBAAoB;IACnC,EAAE,EAAE,MAAM,CAAC;IAEX,UAAU,EAAE,MAAM,CAAC;IAEnB,OAAO,EAAE,OAAO,CAAC;IAEjB,IAAI,EAAE,MAAM,CAAC;IAEb,OAAO,EAAE,iBAAiB,CAAC,OAAO,CAAC;IAEnC,UAAU,EAAE,MAAM,CAAC;IAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC5B;AAED,MAAM,MAAM,oBAAoB,GAC5B,oBAAoB,CAAC,WAAW,GAChC,oBAAoB,CAAC,eAAe,GACpC,oBAAoB,CAAC,gBAAgB,GACrC,oBAAoB,CAAC,cAAc,GACnC,oBAAoB,CAAC,wBAAwB,GAC7C,oBAAoB,CAAC,aAAa,CAAC;AAEvC,yBAAiB,oBAAoB,CAAC;IACpC,UAAiB,WAAW;QAC1B,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,OAAO,EAAE,iBAAiB,CAAC,OAAO,CAAC;QAEnC,IAAI,EAAE,QAAQ,CAAC;QAEf,UAAU,EAAE,MAAM,CAAC;QAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC5B;IAED,UAAiB,eAAe;QAC9B,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,eAAe,CAAC,UAAU,CAAC;QAEvC,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,YAAY,CAAC;QAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC5B;IAED,UAAiB,eAAe,CAAC;QAC/B,UAAiB,UAAU;YACzB;;eAEG;YACH,oBAAoB,EAAE,OAAO,CAAC;YAE9B;;;eAGG;YACH,SAAS,EAAE,OAAO,CAAC;SACpB;KACF;IAED,UAAiB,gBAAgB;QAC/B,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,aAAa,CAAC;QAEpB,UAAU,EAAE,MAAM,CAAC;QAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC5B;IAED,UAAiB,cAAc;QAC7B,EAAE,EAAE,MAAM,CAAC;QAEX;;;WAGG;QACH,cAAc,EAAE,OAAO,CAAC;QAExB,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,MAAM,EAAE,OAAO,CAAC;QAEhB,IAAI,EAAE,YAAY,CAAC;QAEnB,UAAU,EAAE,MAAM,CAAC;KACpB;IAED,UAAiB,wBAAwB;QACvC,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,sBAAsB,CAAC;QAE7B,UAAU,EAAE,MAAM,CAAC;KACpB;IAED,UAAiB,aAAa;QAC5B,EAAE,EAAE,MAAM,CAAC;QAEX,UAAU,EAAE,MAAM,CAAC;QAEnB,OAAO,EAAE,OAAO,CAAC;QAEjB,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,WAAW,CAAC;QAElB,UAAU,EAAE,MAAM,CAAC;QAEnB,SAAS,EAAE,OAAO,CAAC;QAEnB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KAC5B;CACF;AAED,MAAM,MAAM,oBAAoB,GAAG,OAAO,CAAC;AAE3C,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,OAAO,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,OAAO,EAAE,iBAAiB,CAAC,YAAY,CAAC;IAExC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,MAAM,kBAAkB,GAC1B,kBAAkB,CAAC,QAAQ,GAC3B,kBAAkB,CAAC,QAAQ,GAC3B,kBAAkB,CAAC,QAAQ,CAAC;AAEhC,MAAM,CAAC,OAAO,WAAW,kBAAkB,CAAC;IAC1C,UAAiB,QAAQ;QACvB;;WAEG;QACH,UAAU,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;QAEb;;WAEG;QACH,OAAO,EAAE,iBAAiB,CAAC,YAAY,CAAC;QAExC;;WAEG;QACH,IAAI,EAAE,QAAQ,CAAC;QAEf;;WAEG;QACH,OAAO,CAAC,EAAE,OAAO,CAAC;KACnB;IAED,UAAiB,QAAQ;QACvB;;WAEG;QACH,UAAU,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,IAAI,EAAE,YAAY,CAAC;QAEnB;;WAEG;QACH,OAAO,CAAC,EAAE,OAAO,CAAC;KACnB;IAED,UAAiB,QAAQ;QACvB;;WAEG;QACH,UAAU,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,IAAI,EAAE,aAAa,CAAC;QAEpB;;WAEG;QACH,OAAO,CAAC,EAAE,OAAO,CAAC;KACnB;CACF;AAED,MAAM,WAAW,kBAAkB;IACjC,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,OAAO,EACL,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,kBAAkB,IAAI,kBAAkB,GAC9C,CAAC;CACH"}