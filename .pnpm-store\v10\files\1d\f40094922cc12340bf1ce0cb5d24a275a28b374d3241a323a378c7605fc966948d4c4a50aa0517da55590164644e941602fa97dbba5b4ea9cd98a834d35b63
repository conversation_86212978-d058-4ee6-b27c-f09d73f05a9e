// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../../resource.mjs";
import * as UploadAPI from "./upload.mjs";
import { Upload } from "./upload.mjs";
export class Assets extends APIResource {
    constructor() {
        super(...arguments);
        this.upload = new UploadAPI.Upload(this._client);
    }
}
Assets.Upload = Upload;
//# sourceMappingURL=assets.mjs.map